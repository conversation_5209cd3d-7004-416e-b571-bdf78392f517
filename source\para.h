/********************************************************************
 * COPYRIGHT --  
 ********************************************************************
 * Program: IAEC-4000
 * File: para.c
 * Author: Yu Xiang
 * Created: April 23, 2018
 *******************************************************************/
#ifndef __PARA_H
#define __PARA_H

void Init_Once(void);
 
 void Init_Hardware(void);
 
 void Init_Flag(void);
 
 void Upload_Limit_Option(void);
 
 unsigned char Write_ROM(void);
 
 unsigned char Read_ROM(void);
 
 unsigned char Get_Mode(void);
 
 void Get_Wave_Option(unsigned char* WaveOption);
 
 void Set_Wave_Option(unsigned char* Data);
 
 void Set_Limit_Option(unsigned char* Data);
 
 void Get_Limit_Option(unsigned char* Data);
 
 void intToChar(short int n, unsigned char* Data);
 
 short int charToInt(unsigned char* Data);
 
 void Refresh_Parameters(void);
 
 void Get_Parameters(void);
 // 清理重复函数声明：以下函数已在command.h中声明，此处移除重复声明
 // short int CRC16(unsigned char *Data, int length);  // 重复声明，已移除
 // void Float_To_Byte(float data,unsigned char *pB);  // 重复声明，已移除
 // void Byte_To_Float(float *data,unsigned char * pB);  // 重复声明，已移除
 
 void Set_RTC(unsigned char year, unsigned char month, unsigned char date, 
	 unsigned char hour, unsigned char  minute, unsigned char second);
 
 void Get_RTC(unsigned char* time);
 
extern void Soft_Delay(unsigned int T);
 
 void Init_Watchdog(int T);
 
 void Feed_Dog(void);
 
 unsigned char CharToBcd(unsigned char uc);
 
 unsigned char BcdToChar(unsigned char bcd);
 #endif
 //===========================================================================
// End of file.
//===========================================================================

