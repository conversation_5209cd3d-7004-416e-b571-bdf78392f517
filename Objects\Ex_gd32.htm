<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\Ex_gd32.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\Ex_gd32.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Wed Jul 23 15:56:12 2025
<BR><P>
<H3>Maximum Stack Usage =        384 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
TIMER4_IRQHandler &rArr; TIM5_CC3 &rArr; Net_Rx_Tx &rArr; Net_Command &rArr; Net_Coef &rArr; Read_ROM &rArr; Refresh_Parameters &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[3]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">NMI_Handler</a><BR>
 <LI><a href="#[4]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">HardFault_Handler</a><BR>
 <LI><a href="#[5]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">MemManage_Handler</a><BR>
 <LI><a href="#[6]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[6]">BusFault_Handler</a><BR>
 <LI><a href="#[7]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[7]">UsageFault_Handler</a><BR>
 <LI><a href="#[8]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[8]">SVC_Handler</a><BR>
 <LI><a href="#[9]">DebugMon_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[9]">DebugMon_Handler</a><BR>
 <LI><a href="#[a]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[a]">PendSV_Handler</a><BR>
 <LI><a href="#[b]">SysTick_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[b]">SysTick_Handler</a><BR>
 <LI><a href="#[1e]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1e]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1e]">ADC_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[6]">BusFault_Handler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[22]">CAN0_EWMC_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[20]">CAN0_RX0_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[21]">CAN0_RX1_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[1f]">CAN0_TX_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[4e]">CAN1_EWMC_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[4c]">CAN1_RX0_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[4d]">CAN1_RX1_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[4b]">CAN1_TX_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[5a]">DCI_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[17]">DMA0_Channel0_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[18]">DMA0_Channel1_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[19]">DMA0_Channel2_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[1a]">DMA0_Channel3_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[1b]">DMA0_Channel4_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[1c]">DMA0_Channel5_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[1d]">DMA0_Channel6_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[3b]">DMA0_Channel7_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[44]">DMA1_Channel0_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[45]">DMA1_Channel1_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[46]">DMA1_Channel2_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[47]">DMA1_Channel3_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[48]">DMA1_Channel4_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[50]">DMA1_Channel5_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[51]">DMA1_Channel6_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[52]">DMA1_Channel7_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[9]">DebugMon_Handler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[49]">ENET_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[4a]">ENET_WKUP_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[3c]">EXMC_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[12]">EXTI0_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[34]">EXTI10_15_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[13]">EXTI1_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[14]">EXTI2_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[15]">EXTI3_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[16]">EXTI4_IRQHandler</a> from spi.o(i.EXTI4_IRQHandler) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[23]">EXTI5_9_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[10]">FMC_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[5c]">FPU_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[4]">HardFault_Handler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[2c]">I2C0_ER_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[2b]">I2C0_EV_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[2e]">I2C1_ER_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[2d]">I2C1_EV_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[55]">I2C2_ER_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[54]">I2C2_EV_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[64]">IPA_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[d]">LVD_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[5]">MemManage_Handler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[3]">NMI_Handler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[a]">PendSV_Handler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[11]">RCU_CTC_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[35]">RTC_Alarm_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[f]">RTC_WKUP_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[2]">Reset_Handler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[3d]">SDIO_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[2f]">SPI0_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[30]">SPI1_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[3f]">SPI2_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[5f]">SPI3_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[60]">SPI4_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[61]">SPI5_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[8]">SVC_Handler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[b]">SysTick_Handler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[65]">SystemInit</a> from system_gd32f4xx.o(i.SystemInit) referenced from startup_gd32f450.o(.text)
 <LI><a href="#[e]">TAMPER_STAMP_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[24]">TIMER0_BRK_TIMER8_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[27]">TIMER0_Channel_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[26]">TIMER0_TRG_CMT_TIMER10_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[25]">TIMER0_UP_TIMER9_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[28]">TIMER1_IRQHandler</a> from tim.o(i.TIMER1_IRQHandler) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[29]">TIMER2_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[2a]">TIMER3_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[3e]">TIMER4_IRQHandler</a> from tim.o(i.TIMER4_IRQHandler) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[42]">TIMER5_DAC_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[43]">TIMER6_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[37]">TIMER7_BRK_TIMER11_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[3a]">TIMER7_Channel_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[39]">TIMER7_TRG_CMT_TIMER13_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[38]">TIMER7_UP_TIMER12_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[63]">TLI_ER_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[62]">TLI_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[5b]">TRNG_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[40]">UART3_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[41]">UART4_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[5d]">UART6_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[5e]">UART7_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[31]">USART0_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[32]">USART1_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[33]">USART2_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[53]">USART5_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[4f]">USBFS_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[36]">USBFS_WKUP_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[57]">USBHS_EP1_In_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[56]">USBHS_EP1_Out_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[59]">USBHS_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[58]">USBHS_WKUP_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[7]">UsageFault_Handler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[c]">WWDGT_IRQHandler</a> from startup_gd32f450.o(.text) referenced from startup_gd32f450.o(RESET)
 <LI><a href="#[67]">__main</a> from __main.o(!!!main) referenced from startup_gd32f450.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[67]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[68]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[6a]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[245]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[246]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[247]"></a>__decompress</STRONG> (Thumb, 90 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[248]"></a>__decompress1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[249]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[70]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[6b]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[24a]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[24b]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[24c]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[24d]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[24e]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[24f]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[250]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[251]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[252]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[253]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[254]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[255]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[256]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[257]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[258]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[259]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[25a]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[25b]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[25c]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[25d]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[75]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[25e]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[25f]"></a>__rt_lib_shutdown_fini_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[260]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000009))

<P><STRONG><a name="[261]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000011))

<P><STRONG><a name="[262]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000012))

<P><STRONG><a name="[263]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[264]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000006))

<P><STRONG><a name="[265]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E))

<P><STRONG><a name="[69]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[266]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[6d]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[6f]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[267]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[71]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 352 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; Limit_Check &rArr; VF_Check &rArr; __hardfp_pow &rArr; sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[268]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[7d]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[74]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[269]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[76]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[2]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>CAN0_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN0_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>CAN0_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>CAN0_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>CAN1_EWMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>DCI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA0_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA0_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA0_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA0_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA0_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA0_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA0_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>DMA0_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA1_Channel0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>ENET_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>ENET_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>EXMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>EXTI10_15_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>EXTI5_9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C0_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C0_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>IPA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>LVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>RCU_CTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>TAMPER_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIMER0_BRK_TIMER8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIMER0_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIMER0_TRG_CMT_TIMER10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIMER0_UP_TIMER9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIMER2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIMER3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>TIMER5_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>TIMER6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIMER7_BRK_TIMER11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TIMER7_Channel_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIMER7_TRG_CMT_TIMER13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIMER7_UP_TIMER12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>TLI_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>TLI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>TRNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>UART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>UART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>USART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>USBFS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>USBFS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>USBHS_EP1_In_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>USBHS_EP1_Out_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>USBHS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>USBHS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>WWDGT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[7c]"></a>__user_initial_stackheap</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, startup_gd32f450.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[f2]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fourier_Half
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fourier
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Step_Check
</UL>

<P><STRONG><a name="[26a]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[26b]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[26c]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[26d]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[26e]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[26f]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[78]"></a>__read_errno</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, _rserrno.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>

<P><STRONG><a name="[7a]"></a>__set_errno</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, _rserrno.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __set_errno
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
</UL>

<P><STRONG><a name="[79]"></a>__aeabi_errno_addr</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__read_errno
</UL>

<P><STRONG><a name="[270]"></a>__errno$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[271]"></a>__rt_errno_addr$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[272]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[7b]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[273]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[6e]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[73]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[77]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[274]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[275]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[276]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[7e]"></a>AI</STRONG> (Thumb, 410 bytes, Stack size 136 bytes, ai.o(i.AI))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = AI &rArr; Cal_VI &rArr; Check_Inv_Phase &rArr; Cal_Atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Shift_Data
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RMS_Ifd
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RMS
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_AD
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_VI
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_Omiga
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AI_Record
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_AD2_Sample
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_AD1_Sample
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_IRQHandler
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC3
</UL>

<P><STRONG><a name="[8b]"></a>AI_Record</STRONG> (Thumb, 596 bytes, Stack size 32 bytes, ai.o(i.AI_Record))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = AI_Record &rArr; Get_RTC &rArr; rtc_current_time_get
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_RTC
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Float_To_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AI
</UL>

<P><STRONG><a name="[8e]"></a>AVR</STRONG> (Thumb, 856 bytes, Stack size 32 bytes, pid.o(i.AVR))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = AVR &rArr; U_To_Alpha &rArr; __hardfp_acos &rArr; sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DA_N10_10V
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Wave_12_Flag
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;iPID
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;U_To_Alpha
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Under_Ex_U
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Over_Q_U
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Alpha
</UL>

<P><STRONG><a name="[96]"></a>Bak_PT_Check</STRONG> (Thumb, 198 bytes, Stack size 8 bytes, limit.o(i.Bak_PT_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Bak_PT_Check
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Wave_12_Flag
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bak_V_Check
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Limit_Check
</UL>

<P><STRONG><a name="[1b1]"></a>Bak_Ref_Step_Down</STRONG> (Thumb, 152 bytes, Stack size 0 bytes, ref.o(i.Bak_Ref_Step_Down))
<BR><BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Com
</UL>

<P><STRONG><a name="[1b3]"></a>Bak_Ref_Step_Up</STRONG> (Thumb, 166 bytes, Stack size 0 bytes, ref.o(i.Bak_Ref_Step_Up))
<BR><BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Com
</UL>

<P><STRONG><a name="[97]"></a>Bak_V_Check</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, limit.o(i.Bak_V_Check))
<BR><BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bak_PT_Check
</UL>

<P><STRONG><a name="[1aa]"></a>Balance_Check</STRONG> (Thumb, 392 bytes, Stack size 0 bytes, limit.o(i.Balance_Check))
<BR><BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PT_Check
</UL>

<P><STRONG><a name="[19e]"></a>BaudRate_Init</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, optical_fiber.o(i.BaudRate_Init))
<BR><BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_fiber_Init
</UL>

<P><STRONG><a name="[111]"></a>BcdToChar</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, para.o(i.BcdToChar))
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_RTC
</UL>

<P><STRONG><a name="[98]"></a>Breaker_Check</STRONG> (Thumb, 158 bytes, Stack size 16 bytes, limit.o(i.Breaker_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = Breaker_Check &rArr; Work_Mode_V
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Wave_12_Flag
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_Load_Flag
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_V
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Limit_Check
</UL>

<P><STRONG><a name="[fa]"></a>Byte_To_Float</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, optical.o(i.Byte_To_Float))
<BR><BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Scr_Optical
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DeEx_Optical
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Bak_Ch_Optical
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_Parameters
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Coef
</UL>

<P><STRONG><a name="[9b]"></a>CL1606a_Init</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, cl1606_spi.o(i.CL1606a_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = CL1606a_Init &rArr; CL1606a_PIN_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Func
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_PIN_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Hardware
</UL>

<P><STRONG><a name="[a9]"></a>CL1606a_Start_Convst</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, cl1606_spi.o(i.CL1606a_Start_Convst))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CL1606a_Start_Convst
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Func
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_AD1_Sample
</UL>

<P><STRONG><a name="[aa]"></a>CL1606b_Init</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, cl1606_spi.o(i.CL1606b_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = CL1606b_Init &rArr; CL1606b_PIN_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Func
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_PIN_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Hardware
</UL>

<P><STRONG><a name="[ae]"></a>CL1606b_Start_Convst</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, cl1606_spi.o(i.CL1606b_Start_Convst))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = CL1606b_Start_Convst
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Func
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_AD2_Sample
</UL>

<P><STRONG><a name="[105]"></a>CRC16</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, command.o(i.CRC16))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Upload_Ex_Data
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Upload_AD_Const
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_AD
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Search_Command
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_Comm
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Status_Optical
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Scr_Sensor_ID
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DeEx_Optical
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Upload_Wave_Info
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_ROM
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Parameters
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Version
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Status
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_SCR_Sensor_ID
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_SCR_E
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_SCR_C
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Ref
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_PSS4B_L
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_PSS4B_I
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_PSS4B_H
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_PSS
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_PID_Ref
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Misc
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_List
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Limit
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Discharge
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Data
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Command
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;build_ready_data
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Process_Recv_Frame
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Upload_Wave
</UL>

<P><STRONG><a name="[af]"></a>Cal_Atan</STRONG> (Thumb, 180 bytes, Stack size 40 bytes, ai.o(i.Cal_Atan))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = Cal_Atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_Inv_Phase
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_Omiga
</UL>

<P><STRONG><a name="[227]"></a>Cal_Avg</STRONG> (Thumb, 450 bytes, Stack size 0 bytes, ai.o(i.Cal_Avg))
<BR><BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8a]"></a>Cal_Omiga</STRONG> (Thumb, 616 bytes, Stack size 64 bytes, ai.o(i.Cal_Omiga))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = Cal_Omiga &rArr; Cal_Atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fourier_Half
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_Atan
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AI
</UL>

<P><STRONG><a name="[205]"></a>Cal_Period</STRONG> (Thumb, 230 bytes, Stack size 44 bytes, syn.o(i.Cal_Period))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = Cal_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC4
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC2
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC1
</UL>

<P><STRONG><a name="[86]"></a>Cal_VI</STRONG> (Thumb, 1192 bytes, Stack size 40 bytes, ai.o(i.Cal_VI))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = Cal_VI &rArr; Check_Inv_Phase &rArr; Cal_Atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I_Degree_Adjust
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Medium_Value
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_F_Correct
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Fourier
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_Inv_Phase
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Upload_AD_Const
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PT_Check
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_AD
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AI
</UL>

<P><STRONG><a name="[17a]"></a>Cancel_Test</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, command.o(i.Cancel_Test))
<BR><BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Record_Check
</UL>

<P><STRONG><a name="[1c0]"></a>Ch_OK_Check</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, dido.o(i.Ch_OK_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Ch_OK_Check
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_DO_Flag
</UL>

<P><STRONG><a name="[89]"></a>Check_AD</STRONG> (Thumb, 112 bytes, Stack size 8 bytes, ai.o(i.Check_AD))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Check_AD &rArr; Reset_CL1606b
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Reset_CL1606b
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Reset_CL1606a
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AI
</UL>

<P><STRONG><a name="[b8]"></a>Check_Inv_Phase</STRONG> (Thumb, 382 bytes, Stack size 32 bytes, ai.o(i.Check_Inv_Phase))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = Check_Inv_Phase &rArr; Cal_Atan &rArr; __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_Atan
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_VI
</UL>

<P><STRONG><a name="[f5]"></a>Check_Limit_Stop</STRONG> (Thumb, 164 bytes, Stack size 0 bytes, pid.o(i.Check_Limit_Stop))
<BR><BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Alpha
</UL>

<P><STRONG><a name="[198]"></a>Check_Optical_DIP</STRONG> (Thumb, 86 bytes, Stack size 0 bytes, optical.o(i.Check_Optical_DIP))
<BR><BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_Comm
</UL>

<P><STRONG><a name="[146]"></a>Check_Wave_60</STRONG> (Thumb, 84 bytes, Stack size 0 bytes, limit.o(i.Check_Wave_60))
<BR><BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Limit_Check
</UL>

<P><STRONG><a name="[be]"></a>Clear_Comm_Cycle</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, optical.o(i.Clear_Comm_Cycle))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = Clear_Comm_Cycle &rArr; Get_Scr_Ifd &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Scr_Ifd
</UL>
<BR>[Called By]<UL><LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_Comm_Cycle
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Scr_Optical
</UL>

<P><STRONG><a name="[196]"></a>Clear_Comm_Err_Module</STRONG> (Thumb, 110 bytes, Stack size 0 bytes, optical.o(i.Clear_Comm_Err_Module))
<BR><BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_Comm
</UL>

<P><STRONG><a name="[1c4]"></a>Clear_DeEx_Fail_Flag</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, dido.o(i.Clear_DeEx_Fail_Flag))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_DO_Flag
</UL>

<P><STRONG><a name="[195]"></a>Clear_Optical_Data</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, optical.o(i.Clear_Optical_Data))
<BR><BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_Comm
</UL>

<P><STRONG><a name="[161]"></a>Clear_Over_V_Flag</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, dido.o(i.Clear_Over_V_Flag))
<BR><BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stop_Machine_Check
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Start_Flag
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Ctrl
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Mode
</UL>

<P><STRONG><a name="[20f]"></a>Clear_Start_Flag</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, mode.o(i.Clear_Start_Flag))
<BR><BR>[Called By]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Alpha
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_V
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Ifd
</UL>

<P><STRONG><a name="[12f]"></a>Clear_Syn_Error</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, syn.o(i.Clear_Syn_Error))
<BR><BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Syn_Pulse_Init
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Limit
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Syn_Check
</UL>

<P><STRONG><a name="[c0]"></a>Collect_Data</STRONG> (Thumb, 254 bytes, Stack size 8 bytes, syn.o(i.Collect_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Collect_Data &rArr; Pre_Tx_Wave &rArr; Upload_Wave_Info &rArr; CRC16
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wave_Info
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Store_Data
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Pre_Tx_Wave
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c4]"></a>Collect_Scr_Sensor_ID</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, optical.o(i.Collect_Scr_Sensor_ID))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = Collect_Scr_Sensor_ID &rArr; Net_SCR_Sensor_ID &rArr; CRC16
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_SCR_Sensor_ID
</UL>
<BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Search_Command
</UL>

<P><STRONG><a name="[94]"></a>DA_N10_10V</STRONG> (Thumb, 144 bytes, Stack size 24 bytes, ad57x4.o(i.DA_N10_10V))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = DA_N10_10V &rArr; WriteToSPI_IO &rArr; SPI_WriteByte &rArr; SPI_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WriteToSPI_IO
</UL>
<BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PSS2A
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Alpha
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AVR
</UL>

<P><STRONG><a name="[c9]"></a>DI</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, dido.o(i.DI))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = DI &rArr; Get_DI3 &rArr; Work_Mode_Cos &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_DI_Flag
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DI8
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DI7
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DI6
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DI5
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DI4
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DI3
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DI2
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DI1
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DIDO
</UL>

<P><STRONG><a name="[1ba]"></a>DI16_Input</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, gpio.o(i.DI16_Input))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DI16_Input
</UL>
<BR>[Called By]<UL><LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_DI
</UL>

<P><STRONG><a name="[d3]"></a>DIDO</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, dido.o(i.DIDO))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = DIDO &rArr; DI &rArr; Get_DI3 &rArr; Work_Mode_Cos &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d5]"></a>DIP_Switch_Init</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, gpio.o(i.DIP_Switch_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = DIP_Switch_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO
</UL>

<P><STRONG><a name="[d6]"></a>DI_AVR</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dido.o(i.DI_AVR))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DI_AVR &rArr; Work_Mode_V
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_V
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DI2
</UL>

<P><STRONG><a name="[fc]"></a>DI_Ch1</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, dido.o(i.DI_Ch1))
<BR><BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DI1
</UL>

<P><STRONG><a name="[fd]"></a>DI_Ch2</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, dido.o(i.DI_Ch2))
<BR><BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DI1
</UL>

<P><STRONG><a name="[101]"></a>DI_Emergency_Stop</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, dido.o(i.DI_Emergency_Stop))
<BR><BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DI2
</UL>

<P><STRONG><a name="[d7]"></a>DI_FCR</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dido.o(i.DI_FCR))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DI_FCR &rArr; Work_Mode_Ifd
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Ifd
</UL>
<BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DI2
</UL>

<P><STRONG><a name="[ff]"></a>DI_PSS_Off</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, dido.o(i.DI_PSS_Off))
<BR><BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DI1
</UL>

<P><STRONG><a name="[fe]"></a>DI_PSS_On</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, dido.o(i.DI_PSS_On))
<BR><BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DI1
</UL>

<P><STRONG><a name="[d9]"></a>DI_Start</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, dido.o(i.DI_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DI_Start &rArr; Set_Start_Flag
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Start_Flag
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DI1
</UL>

<P><STRONG><a name="[fb]"></a>DI_Stop</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, dido.o(i.DI_Stop))
<BR><BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DI1
</UL>

<P><STRONG><a name="[d4]"></a>DO</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, dido.o(i.DO))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = DO &rArr; DO_Out &rArr; DO16_Out
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO_Out
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_DO_Flag
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO8
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO7
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO6
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO5
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO4
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO3
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO2
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO1
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DIDO
</UL>

<P><STRONG><a name="[dc]"></a>DO1</STRONG> (Thumb, 128 bytes, Stack size 8 bytes, dido.o(i.DO1))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DO1
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO
</UL>

<P><STRONG><a name="[e5]"></a>DO16_Out</STRONG> (Thumb, 52 bytes, Stack size 12 bytes, gpio.o(i.DO16_Out))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DO16_Out
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO_Out
</UL>

<P><STRONG><a name="[dd]"></a>DO2</STRONG> (Thumb, 202 bytes, Stack size 8 bytes, dido.o(i.DO2))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DO2
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO
</UL>

<P><STRONG><a name="[de]"></a>DO3</STRONG> (Thumb, 164 bytes, Stack size 8 bytes, dido.o(i.DO3))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DO3
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO
</UL>

<P><STRONG><a name="[df]"></a>DO4</STRONG> (Thumb, 150 bytes, Stack size 8 bytes, dido.o(i.DO4))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DO4
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO
</UL>

<P><STRONG><a name="[e0]"></a>DO5</STRONG> (Thumb, 162 bytes, Stack size 8 bytes, dido.o(i.DO5))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DO5
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO
</UL>

<P><STRONG><a name="[e1]"></a>DO6</STRONG> (Thumb, 180 bytes, Stack size 8 bytes, dido.o(i.DO6))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DO6
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO
</UL>

<P><STRONG><a name="[e2]"></a>DO7</STRONG> (Thumb, 220 bytes, Stack size 8 bytes, dido.o(i.DO7))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DO7
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO
</UL>

<P><STRONG><a name="[e3]"></a>DO8</STRONG> (Thumb, 204 bytes, Stack size 8 bytes, dido.o(i.DO8))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DO8
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO
</UL>

<P><STRONG><a name="[1c5]"></a>DO_Lock_Check</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, dido.o(i.DO_Lock_Check))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_DO_Flag
</UL>

<P><STRONG><a name="[e4]"></a>DO_Out</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, gpio.o(i.DO_Out))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = DO_Out &rArr; DO16_Out
</UL>
<BR>[Calls]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO16_Out
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO
</UL>

<P><STRONG><a name="[11f]"></a>DelayT</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ad57x4.o(i.DelayT))
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_DA
</UL>

<P><STRONG><a name="[9f]"></a>Delay_Func</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, cl1606_spi.o(i.Delay_Func))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Reset_CL1606b
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Reset_CL1606a
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_Start_Convst
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_Init
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_Start_Convst
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_Init
</UL>

<P><STRONG><a name="[1db]"></a>Delay_IO</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ad57x4.o(i.Delay_IO))
<BR><BR>[Called By]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_ReadWriteByte
</UL>

<P><STRONG><a name="[1ae]"></a>Delay_W5100</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, internet.o(i.Delay_W5100))
<BR><BR>[Called By]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_W5500
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Byte_W5500
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_W5500
</UL>

<P><STRONG><a name="[1c6]"></a>Delayed_DI</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gpio.o(i.Delayed_DI))
<BR><BR>[Called By]<UL><LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_Delayed_DI
</UL>

<P><STRONG><a name="[16]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 158 bytes, Stack size 8 bytes, spi.o(i.EXTI4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = EXTI4_IRQHandler &rArr; Process_Recv_Frame &rArr; CRC16
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_get
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_flag_clear
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Rx
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Process_Recv_Frame
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[ed]"></a>E_Brake_Off_Check</STRONG> (Thumb, 180 bytes, Stack size 8 bytes, pid.o(i.E_Brake_Off_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = E_Brake_Off_Check &rArr; Work_Mode_V
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Wave_12_Flag
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_V
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Alpha
</UL>

<P><STRONG><a name="[ee]"></a>E_Brake_On_Check</STRONG> (Thumb, 392 bytes, Stack size 8 bytes, pid.o(i.E_Brake_On_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = E_Brake_On_Check &rArr; Work_Mode_Ifd
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Wave_12_Flag
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Ifd
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Alpha
</UL>

<P><STRONG><a name="[1bf]"></a>Ex_Fault_Check</STRONG> (Thumb, 110 bytes, Stack size 0 bytes, dido.o(i.Ex_Fault_Check))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_DO_Flag
</UL>

<P><STRONG><a name="[1be]"></a>Ex_Limit_Check</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, dido.o(i.Ex_Limit_Check))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_DO_Flag
</UL>

<P><STRONG><a name="[1bd]"></a>Ex_Ready_Check</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, dido.o(i.Ex_Ready_Check))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_DO_Flag
</UL>

<P><STRONG><a name="[1c2]"></a>Ex_Status_Check</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, dido.o(i.Ex_Status_Check))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_DO_Flag
</UL>

<P><STRONG><a name="[1c1]"></a>Ex_Trip_Check</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, dido.o(i.Ex_Trip_Check))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_DO_Flag
</UL>

<P><STRONG><a name="[ef]"></a>FCR</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, pid.o(i.FCR))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = FCR &rArr; U_To_Alpha &rArr; __hardfp_acos &rArr; sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;U_To_Alpha
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Alpha
</UL>

<P><STRONG><a name="[f0]"></a>FPGA_DONE_Init</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, optical_fiber.o(i.FPGA_DONE_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = FPGA_DONE_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_fiber_Init
</UL>

<P><STRONG><a name="[f1]"></a>FPGA_RST_Init</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, optical_fiber.o(i.FPGA_RST_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = FPGA_RST_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_fiber_Init
</UL>

<P><STRONG><a name="[1a2]"></a>Filter</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, pss.o(i.Filter))
<BR><BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PSS2A
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wash_Out
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Phase_Adjust
</UL>

<P><STRONG><a name="[1a8]"></a>Filter_T</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, pss4b.o(i.Filter_T))
<BR><BR>[Called By]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wash_Out_T
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Phase_Adjust_T
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PSS4B_Input
</UL>

<P><STRONG><a name="[8d]"></a>Float_To_Byte</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, optical.o(i.Float_To_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Float_To_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AI_Record
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_AD
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Parameters
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Status
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Ref
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_PSS4B_L
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_PSS4B_I
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_PSS4B_H
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_PSS
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_PID_Ref
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Misc
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Limit
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;build_ready_data
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Store_Data
</UL>

<P><STRONG><a name="[b4]"></a>Fourier</STRONG> (Thumb, 500 bytes, Stack size 56 bytes, ai.o(i.Fourier))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = Fourier &rArr; __aeabi_memcpy4
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_VI
</UL>

<P><STRONG><a name="[b2]"></a>Fourier_Half</STRONG> (Thumb, 372 bytes, Stack size 48 bytes, ai.o(i.Fourier_Half))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = Fourier_Half &rArr; __aeabi_memcpy4
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_Omiga
</UL>

<P><STRONG><a name="[1e2]"></a>GetSector</STRONG> (Thumb, 374 bytes, Stack size 0 bytes, flash.o(i.GetSector))
<BR><BR>[Called By]<UL><LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFlash_Write_Byte
</UL>

<P><STRONG><a name="[f3]"></a>Get_Alpha</STRONG> (Thumb, 566 bytes, Stack size 32 bytes, pid.o(i.Get_Alpha))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = Get_Alpha &rArr; AVR &rArr; U_To_Alpha &rArr; __hardfp_acos &rArr; sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DA_N10_10V
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FCR
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;E_Brake_On_Check
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;E_Brake_Off_Check
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_Limit_Stop
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AVR
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f7]"></a>Get_Bak_Ch_Optical</STRONG> (Thumb, 734 bytes, Stack size 40 bytes, optical.o(i.Get_Bak_Ch_Optical))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = Get_Bak_Ch_Optical &rArr; __aeabi_ddiv
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Byte_To_Float
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Search_Command
</UL>

<P><STRONG><a name="[cb]"></a>Get_DI1</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, dido.o(i.Get_DI1))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Get_DI1 &rArr; DI_Start &rArr; Set_Start_Flag
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Online_Check
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI_Stop
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI_Start
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI_PSS_On
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI_PSS_Off
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI_Ch2
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI_Ch1
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI
</UL>

<P><STRONG><a name="[cc]"></a>Get_DI2</STRONG> (Thumb, 166 bytes, Stack size 16 bytes, dido.o(i.Get_DI2))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Get_DI2 &rArr; DI_FCR &rArr; Work_Mode_Ifd
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI_FCR
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI_Emergency_Stop
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI_AVR
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI
</UL>

<P><STRONG><a name="[cd]"></a>Get_DI3</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, dido.o(i.Get_DI3))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = Get_DI3 &rArr; Work_Mode_Cos &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Q
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Cos
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI
</UL>

<P><STRONG><a name="[ce]"></a>Get_DI4</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, dido.o(i.Get_DI4))
<BR><BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI
</UL>

<P><STRONG><a name="[cf]"></a>Get_DI5</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, dido.o(i.Get_DI5))
<BR><BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI
</UL>

<P><STRONG><a name="[d0]"></a>Get_DI6</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, dido.o(i.Get_DI6))
<BR><BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI
</UL>

<P><STRONG><a name="[d1]"></a>Get_DI7</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, dido.o(i.Get_DI7))
<BR><BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI
</UL>

<P><STRONG><a name="[d2]"></a>Get_DI8</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, dido.o(i.Get_DI8))
<BR><BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI
</UL>

<P><STRONG><a name="[104]"></a>Get_DeEx_Optical</STRONG> (Thumb, 396 bytes, Stack size 24 bytes, optical.o(i.Get_DeEx_Optical))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Get_DeEx_Optical &rArr; CRC16
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Byte_To_Float
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Discharge_KB
</UL>
<BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Search_Command
</UL>

<P><STRONG><a name="[1ed]"></a>Get_DiDo</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, syn.o(i.Get_DiDo))
<BR><BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Store_Data
</UL>

<P><STRONG><a name="[b3]"></a>Get_F_Correct</STRONG> (Thumb, 238 bytes, Stack size 0 bytes, ai.o(i.Get_F_Correct))
<BR><BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_VI
</UL>

<P><STRONG><a name="[10c]"></a>Get_Limit_Option</STRONG> (Thumb, 444 bytes, Stack size 0 bytes, para.o(i.Get_Limit_Option))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Parameters
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Misc
</UL>

<P><STRONG><a name="[b7]"></a>Get_Medium_Value</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, ai.o(i.Get_Medium_Value))
<BR><BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RMS_Ifd
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RMS
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_VI
</UL>

<P><STRONG><a name="[10e]"></a>Get_Mode</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, para.o(i.Get_Mode))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Parameters
</UL>

<P><STRONG><a name="[107]"></a>Get_Online_Data</STRONG> (Thumb, 218 bytes, Stack size 8 bytes, optical.o(i.Get_Online_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Get_Online_Data &rArr; Init_Flag &rArr; Init_Mode &rArr; Work_Mode_V
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Flag
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_Own_Data
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_Comm
</UL>

<P><STRONG><a name="[91]"></a>Get_Over_Q_U</STRONG> (Thumb, 340 bytes, Stack size 72 bytes, pid.o(i.Get_Over_Q_U))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = Get_Over_Q_U &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Wave_12_Flag
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AVR
</UL>

<P><STRONG><a name="[10b]"></a>Get_Parameters</STRONG> (Thumb, 2058 bytes, Stack size 8 bytes, para.o(i.Get_Parameters))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Get_Parameters &rArr; Float_To_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Float_To_Byte
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;intToChar
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Mode
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Wave_Option
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Limit_Option
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_ROM
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_List
</UL>

<P><STRONG><a name="[8c]"></a>Get_RTC</STRONG> (Thumb, 88 bytes, Stack size 32 bytes, para.o(i.Get_RTC))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = Get_RTC &rArr; rtc_current_time_get
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BcdToChar
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AI_Record
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Ref
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wave_Info
</UL>

<P><STRONG><a name="[bf]"></a>Get_Scr_Ifd</STRONG> (Thumb, 246 bytes, Stack size 48 bytes, optical.o(i.Get_Scr_Ifd))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = Get_Scr_Ifd &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Clear_Comm_Cycle
</UL>

<P><STRONG><a name="[112]"></a>Get_Scr_Optical</STRONG> (Thumb, 1180 bytes, Stack size 40 bytes, optical.o(i.Get_Scr_Optical))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = Get_Scr_Optical &rArr; Clear_Comm_Cycle &rArr; Get_Scr_Ifd &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Clear_Comm_Cycle
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Byte_To_Float
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Search_Command
</UL>

<P><STRONG><a name="[113]"></a>Get_Scr_Sensor_ID</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, optical.o(i.Get_Scr_Sensor_ID))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Get_Scr_Sensor_ID &rArr; CRC16
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_Comm
</UL>

<P><STRONG><a name="[114]"></a>Get_Status_Optical</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, optical.o(i.Get_Status_Optical))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Get_Status_Optical &rArr; CRC16
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_Comm
</UL>

<P><STRONG><a name="[90]"></a>Get_Under_Ex_U</STRONG> (Thumb, 874 bytes, Stack size 40 bytes, pid.o(i.Get_Under_Ex_U))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = Get_Under_Ex_U &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Wave_12_Flag
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AVR
</UL>

<P><STRONG><a name="[10d]"></a>Get_Wave_Option</STRONG> (Thumb, 254 bytes, Stack size 0 bytes, para.o(i.Get_Wave_Option))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Parameters
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Misc
</UL>

<P><STRONG><a name="[b6]"></a>I_Degree_Adjust</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, ai.o(i.I_Degree_Adjust))
<BR><BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_VI
</UL>

<P><STRONG><a name="[115]"></a>Init_Coef</STRONG> (Thumb, 2122 bytes, Stack size 48 bytes, para.o(i.Init_Coef))
<BR><BR>[Stack]<UL><LI>Max Depth = 296<LI>Call Chain = Init_Coef &rArr; Read_ROM &rArr; Refresh_Parameters &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Mode
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Start
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_PSS4B_Coef
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Wave_Option
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Limit_Option
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_ROM
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PSS2A_T_K
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Hardware
</UL>

<P><STRONG><a name="[11d]"></a>Init_DA</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, ad57x4.o(i.Init_DA))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Init_DA &rArr; SPI_IoInit &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DelayT
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_IoInit
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Hardware
</UL>

<P><STRONG><a name="[132]"></a>Init_DIDO</STRONG> (Thumb, 570 bytes, Stack size 0 bytes, para.o(i.Init_DIDO))
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Once
</UL>

<P><STRONG><a name="[124]"></a>Init_DIDO_Para</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, gpio.o(i.Init_DIDO_Para))
<BR><BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO
</UL>

<P><STRONG><a name="[122]"></a>Init_DO_Test</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, command.o(i.Init_DO_Test))
<BR><BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Flag
</UL>

<P><STRONG><a name="[120]"></a>Init_Ex</STRONG> (Thumb, 384 bytes, Stack size 8 bytes, start.o(i.Init_Ex))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Init_Ex
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Wave_12_Flag
</UL>
<BR>[Called By]<UL><LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Check
</UL>

<P><STRONG><a name="[109]"></a>Init_Flag</STRONG> (Thumb, 250 bytes, Stack size 8 bytes, para.o(i.Init_Flag))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Init_Flag &rArr; Init_Mode &rArr; Work_Mode_V
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Mode
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Limit
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Start
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_DO_Test
</UL>
<BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stop_Machine_Check
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Once
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Over_V_Check
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Low_F_Check
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Online_Data
</UL>

<P><STRONG><a name="[123]"></a>Init_GPIO</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, gpio.o(i.Init_GPIO))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Init_GPIO &rArr; DIP_Switch_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_DIP_Switch
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_DIDO_Para
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DIP_Switch_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Hardware
</UL>

<P><STRONG><a name="[126]"></a>Init_Hardware</STRONG> (Thumb, 118 bytes, Stack size 8 bytes, para.o(i.Init_Hardware))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = Init_Hardware &rArr; Init_Coef &rArr; Read_ROM &rArr; Refresh_Parameters &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_Init
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_Init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_DA
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_fiber_Init
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SRAM_Init
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Syn_Pulse_Init
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_DI_Flag
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMx_Init
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Input_Init
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_RTC
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Online
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Coef
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Net
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[121]"></a>Init_Limit</STRONG> (Thumb, 286 bytes, Stack size 8 bytes, limit.o(i.Init_Limit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Init_Limit
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Clear_Syn_Error
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Flag
</UL>

<P><STRONG><a name="[11a]"></a>Init_Mode</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, mode.o(i.Init_Mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Init_Mode &rArr; Work_Mode_V
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_V
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Ifd
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Flag
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Coef
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_Parameters
</UL>

<P><STRONG><a name="[12a]"></a>Init_Net</STRONG> (Thumb, 358 bytes, Stack size 112 bytes, net.o(i.Init_Net))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = Init_Net &rArr; Network_Init &rArr; SPI2_DMA_Config &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Network_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Hardware
</UL>

<P><STRONG><a name="[131]"></a>Init_Once</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, para.o(i.Init_Once))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Init_Once &rArr; Init_Flag &rArr; Init_Mode &rArr; Work_Mode_V
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Flag
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_PSS2A
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_PID
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Wave
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_DIDO
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[129]"></a>Init_Online</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, para.o(i.Init_Online))
<BR><BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Hardware
</UL>

<P><STRONG><a name="[133]"></a>Init_PID</STRONG> (Thumb, 318 bytes, Stack size 0 bytes, pid.o(i.Init_PID))
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Once
</UL>

<P><STRONG><a name="[134]"></a>Init_PSS2A</STRONG> (Thumb, 84 bytes, Stack size 0 bytes, pss.o(i.Init_PSS2A))
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Once
</UL>

<P><STRONG><a name="[11b]"></a>Init_PSS4B_Coef</STRONG> (Thumb, 522 bytes, Stack size 0 bytes, pss4b.o(i.Init_PSS4B_Coef))
<BR><BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Coef
</UL>

<P><STRONG><a name="[12b]"></a>Init_RTC</STRONG> (Thumb, 184 bytes, Stack size 24 bytes, para.o(i.Init_RTC))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = Init_RTC &rArr; rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_sync_wait
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_flag_clear
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_rtc_clock_config
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_on
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_all_reset_flag_clear
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pmu_backup_write_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Hardware
</UL>

<P><STRONG><a name="[119]"></a>Init_Start</STRONG> (Thumb, 118 bytes, Stack size 0 bytes, start.o(i.Init_Start))
<BR><BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Flag
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Coef
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_Parameters
</UL>

<P><STRONG><a name="[135]"></a>Init_Wave</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, para.o(i.Init_Wave))
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Once
</UL>

<P><STRONG><a name="[13e]"></a>Init_Wave_Info</STRONG> (Thumb, 46 bytes, Stack size 72 bytes, syn.o(i.Init_Wave_Info))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = Init_Wave_Info &rArr; SRAM_WriteBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SRAM_WriteBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Syn_Pulse_Init
</UL>

<P><STRONG><a name="[140]"></a>Limit_Check</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, limit.o(i.Limit_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = Limit_Check &rArr; VF_Check &rArr; __hardfp_pow &rArr; sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VF_Check
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_PT_Check
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Short_Circuit_Check
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Scr_Exit_Check
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Limit_Check
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Over_V_Check
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Over_I_Check
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Over_Ex_Check
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Low_F_Check
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_Wave_60
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breaker_Check
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bak_PT_Check
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[149]"></a>Low_F_Check</STRONG> (Thumb, 112 bytes, Stack size 8 bytes, limit.o(i.Low_F_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = Low_F_Check &rArr; Syn_Pulse_Init &rArr; Init_Wave_Info &rArr; SRAM_WriteBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Syn_Pulse_Init
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Flag
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Limit_Check
</UL>

<P><STRONG><a name="[20e]"></a>Mode_Save</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, mode.o(i.Mode_Save))
<BR><BR>[Called By]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Alpha
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_V
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Q
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Ifd
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Cos
</UL>

<P><STRONG><a name="[bb]"></a>Net_AD</STRONG> (Thumb, 574 bytes, Stack size 32 bytes, net.o(i.Net_AD))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Net_AD &rArr; Float_To_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Float_To_Byte
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_VI
</UL>

<P><STRONG><a name="[14b]"></a>Net_Coef</STRONG> (Thumb, 6162 bytes, Stack size 88 bytes, net.o(i.Net_Coef))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = Net_Coef &rArr; Read_ROM &rArr; Refresh_Parameters &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Wave_Option
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_RTC
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Limit_Option
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_ROM
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PSS2A_T_K
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Byte_To_Float
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Ref
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_PSS4B_L
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_PSS4B_I
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_PSS4B_H
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_PSS
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_PID_Ref
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Misc
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Limit
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Command
</UL>

<P><STRONG><a name="[155]"></a>Net_Comm</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, internet.o(i.Net_Comm))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = Net_Comm &rArr; Net_Cycle &rArr; Net_Mode_Select &rArr; Socket_Correct_Addr &rArr; Write_W5500 &rArr; spi_rw_byte_spi2
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Tx
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Rx
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_Tx
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_Rx
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Cycle
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Rx_Tx
</UL>

<P><STRONG><a name="[15b]"></a>Net_Command</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, net.o(i.Net_Command))
<BR><BR>[Stack]<UL><LI>Max Depth = 360<LI>Call Chain = Net_Command &rArr; Net_Coef &rArr; Read_ROM &rArr; Refresh_Parameters &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Upload
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Scr
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_List
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Discharge_Para
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Ctrl
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Coef
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Rx_Tx
</UL>

<P><STRONG><a name="[15c]"></a>Net_Ctrl</STRONG> (Thumb, 318 bytes, Stack size 16 bytes, net.o(i.Net_Ctrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = Net_Ctrl &rArr; Switch_Operating_Mode &rArr; Work_Mode_Cos &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Start_Flag
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Clear_Over_V_Flag
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Switch_Start_Mode
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Switch_Operating_Mode
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Step_Check
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Test
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Record_Check
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Command
</UL>

<P><STRONG><a name="[156]"></a>Net_Cycle</STRONG> (Thumb, 152 bytes, Stack size 16 bytes, internet.o(i.Net_Cycle))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = Net_Cycle &rArr; Net_Mode_Select &rArr; Socket_Correct_Addr &rArr; Write_W5500 &rArr; spi_rw_byte_spi2
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_State_Check
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Mode_Select
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Byte_W5500
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Comm
</UL>

<P><STRONG><a name="[158]"></a>Net_DMA_Rx</STRONG> (Thumb, 182 bytes, Stack size 16 bytes, internet.o(i.Net_DMA_Rx))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Net_DMA_Rx &rArr; Net_DMA_rx_process &rArr; Write_Byte_W5500 &rArr; spi_rw_byte_spi2
</UL>
<BR>[Calls]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_rx_process
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Comm
</UL>

<P><STRONG><a name="[157]"></a>Net_DMA_Tx</STRONG> (Thumb, 200 bytes, Stack size 24 bytes, internet.o(i.Net_DMA_Tx))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = Net_DMA_Tx &rArr; Net_DMA_tx_process &rArr; Write_Byte_W5500 &rArr; spi_rw_byte_spi2
</UL>
<BR>[Calls]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_tx_process
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Comm
</UL>

<P><STRONG><a name="[169]"></a>Net_DMA_rx_process</STRONG> (Thumb, 546 bytes, Stack size 32 bytes, internet.o(i.Net_DMA_rx_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = Net_DMA_rx_process &rArr; Write_Byte_W5500 &rArr; spi_rw_byte_spi2
</UL>
<BR>[Calls]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_dma_enable
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_dma_disable
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_disable
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Byte_W5500
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_Byte_W5500
</UL>
<BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_Rx
</UL>

<P><STRONG><a name="[16a]"></a>Net_DMA_tx_process</STRONG> (Thumb, 466 bytes, Stack size 40 bytes, internet.o(i.Net_DMA_tx_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = Net_DMA_tx_process &rArr; Write_Byte_W5500 &rArr; spi_rw_byte_spi2
</UL>
<BR>[Calls]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_disable
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Byte_W5500
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_Tx
</UL>

<P><STRONG><a name="[172]"></a>Net_Data</STRONG> (Thumb, 192 bytes, Stack size 24 bytes, net.o(i.Net_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Net_Data &rArr; CRC16
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Rx_Tx
</UL>

<P><STRONG><a name="[173]"></a>Net_Discharge</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, net.o(i.Net_Discharge))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Net_Discharge &rArr; CRC16
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Upload
</UL>

<P><STRONG><a name="[106]"></a>Net_Discharge_KB</STRONG> (Thumb, 46 bytes, Stack size 12 bytes, net.o(i.Net_Discharge_KB))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = Net_Discharge_KB
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DeEx_Optical
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Upload
</UL>

<P><STRONG><a name="[160]"></a>Net_Discharge_Para</STRONG> (Thumb, 116 bytes, Stack size 28 bytes, net.o(i.Net_Discharge_Para))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Net_Discharge_Para &rArr; Net_Scr
</UL>
<BR>[Calls]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Scr
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Command
</UL>

<P><STRONG><a name="[174]"></a>Net_Interrupt_Process</STRONG> (Thumb, 486 bytes, Stack size 32 bytes, internet.o(i.Net_Interrupt_Process))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = Net_Interrupt_Process &rArr; Write_Byte_W5500 &rArr; spi_rw_byte_spi2
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Byte_W5500
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_Byte_W5500
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_State_Check
</UL>

<P><STRONG><a name="[14e]"></a>Net_Limit</STRONG> (Thumb, 422 bytes, Stack size 24 bytes, net.o(i.Net_Limit))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Net_Limit &rArr; Float_To_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Float_To_Byte
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Upload
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Coef
</UL>

<P><STRONG><a name="[15e]"></a>Net_List</STRONG> (Thumb, 148 bytes, Stack size 32 bytes, net.o(i.Net_List))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = Net_List &rArr; Refresh_Parameters &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_Parameters
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Parameters
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Ref
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Command
</UL>

<P><STRONG><a name="[14f]"></a>Net_Misc</STRONG> (Thumb, 410 bytes, Stack size 24 bytes, net.o(i.Net_Misc))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Net_Misc &rArr; Float_To_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Float_To_Byte
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Wave_Option
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Limit_Option
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Upload
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Coef
</UL>

<P><STRONG><a name="[167]"></a>Net_Mode_Select</STRONG> (Thumb, 224 bytes, Stack size 16 bytes, internet.o(i.Net_Mode_Select))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = Net_Mode_Select &rArr; Socket_Correct_Addr &rArr; Write_W5500 &rArr; spi_rw_byte_spi2
</UL>
<BR>[Calls]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Socket_Mode
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Socket_Correct_Addr
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Cycle
</UL>

<P><STRONG><a name="[178]"></a>Net_Monitor</STRONG> (Thumb, 176 bytes, Stack size 28 bytes, net.o(i.Net_Monitor))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Net_Monitor &rArr; Refresh_Monitor_Data
</UL>
<BR>[Calls]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_Monitor_Data
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Rx_Tx
</UL>

<P><STRONG><a name="[14c]"></a>Net_PID_Ref</STRONG> (Thumb, 432 bytes, Stack size 24 bytes, net.o(i.Net_PID_Ref))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Net_PID_Ref &rArr; Float_To_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Float_To_Byte
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Upload
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Coef
</UL>

<P><STRONG><a name="[14d]"></a>Net_PSS</STRONG> (Thumb, 440 bytes, Stack size 24 bytes, net.o(i.Net_PSS))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Net_PSS &rArr; Float_To_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Float_To_Byte
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Upload
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Coef
</UL>

<P><STRONG><a name="[152]"></a>Net_PSS4B_H</STRONG> (Thumb, 274 bytes, Stack size 24 bytes, net.o(i.Net_PSS4B_H))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Net_PSS4B_H &rArr; Float_To_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Float_To_Byte
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Upload
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Coef
</UL>

<P><STRONG><a name="[151]"></a>Net_PSS4B_I</STRONG> (Thumb, 170 bytes, Stack size 24 bytes, net.o(i.Net_PSS4B_I))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Net_PSS4B_I &rArr; Float_To_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Float_To_Byte
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Upload
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Coef
</UL>

<P><STRONG><a name="[150]"></a>Net_PSS4B_L</STRONG> (Thumb, 170 bytes, Stack size 24 bytes, net.o(i.Net_PSS4B_L))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Net_PSS4B_L &rArr; Float_To_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Float_To_Byte
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Upload
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Coef
</UL>

<P><STRONG><a name="[18f]"></a>Net_Para_Init</STRONG> (Thumb, 138 bytes, Stack size 0 bytes, internet.o(i.Net_Para_Init))
<BR><BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Network_Init
</UL>

<P><STRONG><a name="[166]"></a>Net_Record_Check</STRONG> (Thumb, 168 bytes, Stack size 32 bytes, net.o(i.Net_Record_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Net_Record_Check &rArr; Upload_Wave_Info &rArr; CRC16
</UL>
<BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SRAM_ReadBuffer
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Upload_Wave_Info
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cancel_Test
</UL>
<BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Ctrl
</UL>

<P><STRONG><a name="[153]"></a>Net_Ref</STRONG> (Thumb, 288 bytes, Stack size 48 bytes, net.o(i.Net_Ref))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = Net_Ref &rArr; Get_RTC &rArr; rtc_current_time_get
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_RTC
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Float_To_Byte
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SRAM_ReadBuffer
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Write_ROM
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Upload
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_List
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Coef
</UL>

<P><STRONG><a name="[15a]"></a>Net_Rx</STRONG> (Thumb, 404 bytes, Stack size 32 bytes, internet.o(i.Net_Rx))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = Net_Rx &rArr; Read_W5500 &rArr; spi_rw_byte_spi2
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Byte_W5500
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_W5500
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_Byte_W5500
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Comm
</UL>

<P><STRONG><a name="[17e]"></a>Net_Rx_Tx</STRONG> (Thumb, 110 bytes, Stack size 8 bytes, net.o(i.Net_Rx_Tx))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = Net_Rx_Tx &rArr; Net_Command &rArr; Net_Coef &rArr; Read_ROM &rArr; Refresh_Parameters &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Comm
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Monitor
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Data
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Command
</UL>
<BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_IRQHandler
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC3
</UL>

<P><STRONG><a name="[17f]"></a>Net_SCR_C</STRONG> (Thumb, 186 bytes, Stack size 32 bytes, net.o(i.Net_SCR_C))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Net_SCR_C &rArr; CRC16
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Upload
</UL>

<P><STRONG><a name="[180]"></a>Net_SCR_E</STRONG> (Thumb, 146 bytes, Stack size 32 bytes, net.o(i.Net_SCR_E))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Net_SCR_E &rArr; CRC16
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Upload
</UL>

<P><STRONG><a name="[c5]"></a>Net_SCR_Sensor_ID</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, net.o(i.Net_SCR_Sensor_ID))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Net_SCR_Sensor_ID &rArr; CRC16
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Collect_Scr_Sensor_ID
</UL>

<P><STRONG><a name="[15f]"></a>Net_Scr</STRONG> (Thumb, 50 bytes, Stack size 20 bytes, net.o(i.Net_Scr))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = Net_Scr
</UL>
<BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Discharge_Para
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Command
</UL>

<P><STRONG><a name="[168]"></a>Net_State_Check</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, internet.o(i.Net_State_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Net_State_Check &rArr; Net_Interrupt_Process &rArr; Write_Byte_W5500 &rArr; spi_rw_byte_spi2
</UL>
<BR>[Calls]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Interrupt_Process
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Cycle
</UL>

<P><STRONG><a name="[181]"></a>Net_Status</STRONG> (Thumb, 388 bytes, Stack size 24 bytes, net.o(i.Net_Status))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Net_Status &rArr; Float_To_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Float_To_Byte
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Upload
</UL>

<P><STRONG><a name="[159]"></a>Net_Tx</STRONG> (Thumb, 406 bytes, Stack size 48 bytes, internet.o(i.Net_Tx))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = Net_Tx &rArr; Write_W5500 &rArr; spi_rw_byte_spi2
</UL>
<BR>[Calls]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_W5500
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Byte_W5500
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_Byte_W5500
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Comm
</UL>

<P><STRONG><a name="[15d]"></a>Net_Upload</STRONG> (Thumb, 242 bytes, Stack size 16 bytes, net.o(i.Net_Upload))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = Net_Upload &rArr; Net_Ref &rArr; Get_RTC &rArr; rtc_current_time_get
</UL>
<BR>[Calls]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Version
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Status
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_SCR_E
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_SCR_C
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Ref
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_PSS4B_L
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_PSS4B_I
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_PSS4B_H
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_PSS
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_PID_Ref
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Misc
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Limit
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Discharge_KB
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Discharge
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Command
</UL>

<P><STRONG><a name="[183]"></a>Net_Version</STRONG> (Thumb, 100 bytes, Stack size 32 bytes, net.o(i.Net_Version))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Net_Version &rArr; CRC16
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Upload
</UL>

<P><STRONG><a name="[184]"></a>Net_Write_ROM</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, net.o(i.Net_Write_ROM))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = Net_Write_ROM &rArr; Net_Ref &rArr; Get_RTC &rArr; rtc_current_time_get
</UL>
<BR>[Calls]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_ROM
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Ref
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[130]"></a>Network_Init</STRONG> (Thumb, 598 bytes, Stack size 72 bytes, internet.o(i.Network_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = Network_Init &rArr; SPI2_DMA_Config &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_Delay
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI2_DMA_Config
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_DMA_Config
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Para_Init
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_W5500
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Byte_W5500
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W5200U2_RST_Off
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W5200U2_RST_Init
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W5200U1_RST_Off
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W5200U1_RST_Init
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI5_For_W5200_Init
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI4_For_W5200_Init
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Config_W5500
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Net
</UL>

<P><STRONG><a name="[100]"></a>Online_Check</STRONG> (Thumb, 486 bytes, Stack size 4 bytes, limit.o(i.Online_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Online_Check
</UL>
<BR>[Calls]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Standby_Check
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DI1
</UL>

<P><STRONG><a name="[191]"></a>Optical_Comm</STRONG> (Thumb, 682 bytes, Stack size 24 bytes, optical.o(i.Optical_Comm))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = Optical_Comm &rArr; Search_Command &rArr; Get_Scr_Optical &rArr; Clear_Comm_Cycle &rArr; Get_Scr_Ifd &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Tx_Buffer
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rx_Buffer
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SelectSynSource
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Search_Command
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_Comm_Cycle
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Status_Optical
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Scr_Sensor_ID
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Online_Data
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Clear_Optical_Data
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Clear_Comm_Err_Module
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_Optical_DIP
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_IRQHandler
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC3
</UL>

<P><STRONG><a name="[192]"></a>Optical_Comm_Cycle</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, optical.o(i.Optical_Comm_Cycle))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = Optical_Comm_Cycle &rArr; Clear_Comm_Cycle &rArr; Get_Scr_Ifd &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Clear_Comm_Cycle
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_Comm
</UL>

<P><STRONG><a name="[19a]"></a>Optical_fiber_FSMC_Init</STRONG> (Thumb, 1668 bytes, Stack size 96 bytes, optical_fiber.o(i.Optical_fiber_FSMC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = Optical_fiber_FSMC_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_norsram_init
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_norsram_enable
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_norsram_consecutive_clock_config
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_fiber_Init
</UL>

<P><STRONG><a name="[127]"></a>Optical_fiber_Init</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, optical_fiber.o(i.Optical_fiber_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = Optical_fiber_Init &rArr; Optical_fiber_FSMC_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_fiber_FSMC_Init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGA_RST_Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGA_DONE_Init
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BaudRate_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Hardware
</UL>

<P><STRONG><a name="[141]"></a>Over_Ex_Check</STRONG> (Thumb, 650 bytes, Stack size 80 bytes, limit.o(i.Over_Ex_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = Over_Ex_Check &rArr; __hardfp_pow &rArr; sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Wave_12_Flag
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Limit_Check
</UL>

<P><STRONG><a name="[147]"></a>Over_I_Check</STRONG> (Thumb, 868 bytes, Stack size 48 bytes, limit.o(i.Over_I_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = Over_I_Check &rArr; __hardfp_pow &rArr; sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Wave_12_Flag
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Limit_Check
</UL>

<P><STRONG><a name="[144]"></a>Over_V_Check</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, limit.o(i.Over_V_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Over_V_Check &rArr; Init_Flag &rArr; Init_Mode &rArr; Work_Mode_V
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Flag
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Wave_12_Flag
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Limit_Check
</UL>

<P><STRONG><a name="[93]"></a>PID</STRONG> (Thumb, 462 bytes, Stack size 12 bytes, pid.o(i.PID))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = PID
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FCR
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AVR
</UL>

<P><STRONG><a name="[1a0]"></a>PSS2A</STRONG> (Thumb, 2276 bytes, Stack size 24 bytes, pss.o(i.PSS2A))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = PSS2A &rArr; DA_N10_10V &rArr; WriteToSPI_IO &rArr; SPI_WriteByte &rArr; SPI_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DA_N10_10V
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wash_Out
<LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Phase_Adjust
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Filter
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[116]"></a>PSS2A_T_K</STRONG> (Thumb, 366 bytes, Stack size 0 bytes, pss.o(i.PSS2A_T_K))
<BR><BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Coef
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_Parameters
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Coef
</UL>

<P><STRONG><a name="[1a4]"></a>PSS4B</STRONG> (Thumb, 610 bytes, Stack size 32 bytes, pss4b.o(i.PSS4B))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = PSS4B &rArr; PSS4B_Branch &rArr; Phase_Adjust_T
</UL>
<BR>[Calls]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PSS4B_Input
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PSS4B_Branch
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1a6]"></a>PSS4B_Branch</STRONG> (Thumb, 266 bytes, Stack size 24 bytes, pss4b.o(i.PSS4B_Branch))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = PSS4B_Branch &rArr; Phase_Adjust_T
</UL>
<BR>[Calls]<UL><LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Phase_Adjust_T
</UL>
<BR>[Called By]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PSS4B
</UL>

<P><STRONG><a name="[1a5]"></a>PSS4B_Input</STRONG> (Thumb, 394 bytes, Stack size 4 bytes, pss4b.o(i.PSS4B_Input))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PSS4B_Input &rArr; Wash_Out_T
</UL>
<BR>[Calls]<UL><LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wash_Out_T
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Filter_T
</UL>
<BR>[Called By]<UL><LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PSS4B
</UL>

<P><STRONG><a name="[b9]"></a>PT_Check</STRONG> (Thumb, 510 bytes, Stack size 32 bytes, limit.o(i.PT_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = PT_Check &rArr; Work_Mode_V
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Wave_12_Flag
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Balance_Check
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_V
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Ifd
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_VI
</UL>

<P><STRONG><a name="[1a3]"></a>Phase_Adjust</STRONG> (Thumb, 60 bytes, Stack size 4 bytes, pss.o(i.Phase_Adjust))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Phase_Adjust
</UL>
<BR>[Calls]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Filter
</UL>
<BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PSS2A
</UL>

<P><STRONG><a name="[1a7]"></a>Phase_Adjust_T</STRONG> (Thumb, 76 bytes, Stack size 4 bytes, pss4b.o(i.Phase_Adjust_T))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Phase_Adjust_T
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Filter_T
</UL>
<BR>[Called By]<UL><LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PSS4B_Branch
</UL>

<P><STRONG><a name="[204]"></a>Phase_Difference</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, syn.o(i.Phase_Difference))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Phase_Difference
</UL>
<BR>[Called By]<UL><LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC4
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC2
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC1
</UL>

<P><STRONG><a name="[c3]"></a>Pre_Tx_Wave</STRONG> (Thumb, 140 bytes, Stack size 24 bytes, syn.o(i.Pre_Tx_Wave))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = Pre_Tx_Wave &rArr; Upload_Wave_Info &rArr; CRC16
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SRAM_WriteBuffer
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Upload_Wave_Info
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Collect_Data
</UL>

<P><STRONG><a name="[1f0]"></a>Primary_Syn_Check</STRONG> (Thumb, 246 bytes, Stack size 0 bytes, syn.o(i.Primary_Syn_Check))
<BR><BR>[Called By]<UL><LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Syn_Check
</UL>

<P><STRONG><a name="[1b5]"></a>Q_Set</STRONG> (Thumb, 180 bytes, Stack size 0 bytes, ref.o(i.Q_Set))
<BR><BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Com
</UL>

<P><STRONG><a name="[87]"></a>RMS</STRONG> (Thumb, 244 bytes, Stack size 88 bytes, ai.o(i.RMS))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = RMS &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Medium_Value
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AI
</UL>

<P><STRONG><a name="[88]"></a>RMS_Ifd</STRONG> (Thumb, 234 bytes, Stack size 88 bytes, ai.o(i.RMS_Ifd))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = RMS_Ifd &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Medium_Value
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AI
</UL>

<P><STRONG><a name="[7f]"></a>Read_AD1_Sample</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, cl1606_spi.o(i.Read_AD1_Sample))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = Read_AD1_Sample &rArr; CL1606a_Read_Reg &rArr; SPI3_RW_One_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_Start_Convst
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_Read_Reg
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AI
</UL>

<P><STRONG><a name="[80]"></a>Read_AD2_Sample</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, cl1606_spi.o(i.Read_AD2_Sample))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = Read_AD2_Sample &rArr; CL1606b_Read_Reg &rArr; SPI4_RW_One_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_Start_Convst
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_Read_Reg
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AI
</UL>

<P><STRONG><a name="[125]"></a>Read_DIP_Switch</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, gpio.o(i.Read_DIP_Switch))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Read_DIP_Switch
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
</UL>
<BR>[Called By]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_GPIO
</UL>

<P><STRONG><a name="[11c]"></a>Read_ROM</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, para.o(i.Read_ROM))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = Read_ROM &rArr; Refresh_Parameters &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFlash_Read_Bytes
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_Parameters
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Coef
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Coef
</UL>

<P><STRONG><a name="[1ca]"></a>Read_State</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, optical_fiber.o(i.Read_State))
<BR><BR>[Called By]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rx_Buffer
</UL>

<P><STRONG><a name="[1af]"></a>Ref_Com</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, ref.o(i.Ref_Com))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = Ref_Com &rArr; Ref_Tracking &rArr; Work_Mode_Cos &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;V_Ref_Q
<LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_Cos_Ref
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Tracking
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Step_Up
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Step_Down
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Q_Set
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bak_Ref_Step_Up
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bak_Ref_Step_Down
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[148]"></a>Ref_Limit_Check</STRONG> (Thumb, 84 bytes, Stack size 0 bytes, limit.o(i.Ref_Limit_Check))
<BR><BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Limit_Check
</UL>

<P><STRONG><a name="[1b0]"></a>Ref_Step_Down</STRONG> (Thumb, 692 bytes, Stack size 40 bytes, ref.o(i.Ref_Step_Down))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = Ref_Step_Down &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_Wait
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Com
</UL>

<P><STRONG><a name="[1b2]"></a>Ref_Step_Up</STRONG> (Thumb, 710 bytes, Stack size 40 bytes, ref.o(i.Ref_Step_Up))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = Ref_Step_Up &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_Wait
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Com
</UL>

<P><STRONG><a name="[1b6]"></a>Ref_Tracking</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, ref.o(i.Ref_Tracking))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = Ref_Tracking &rArr; Work_Mode_Cos &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_V
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Q
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Ifd
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Cos
</UL>
<BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Com
</UL>

<P><STRONG><a name="[1b7]"></a>Refresh_Cos_Ref</STRONG> (Thumb, 144 bytes, Stack size 0 bytes, ref.o(i.Refresh_Cos_Ref))
<BR><BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Com
</UL>

<P><STRONG><a name="[1b9]"></a>Refresh_DI</STRONG> (Thumb, 32 bytes, Stack size 4 bytes, gpio.o(i.Refresh_DI))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = Refresh_DI &rArr; DI16_Input
</UL>
<BR>[Calls]<UL><LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI16_Input
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_DI_Flag
</UL>

<P><STRONG><a name="[ca]"></a>Refresh_DI_Flag</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, dido.o(i.Refresh_DI_Flag))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = Refresh_DI_Flag &rArr; Refresh_Delayed_DI
</UL>
<BR>[Calls]<UL><LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_Delayed_DI
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_DI
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Hardware
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI
</UL>

<P><STRONG><a name="[db]"></a>Refresh_DO_Flag</STRONG> (Thumb, 44 bytes, Stack size 4 bytes, dido.o(i.Refresh_DO_Flag))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = Refresh_DO_Flag &rArr; Ch_OK_Check
</UL>
<BR>[Calls]<UL><LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Running_Signal
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_eBrake_Flag
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ex_Trip_Check
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ex_Status_Check
<LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ex_Ready_Check
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ex_Limit_Check
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ex_Fault_Check
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO_Lock_Check
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Clear_DeEx_Fail_Flag
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ch_OK_Check
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DO
</UL>

<P><STRONG><a name="[1bb]"></a>Refresh_Delayed_DI</STRONG> (Thumb, 54 bytes, Stack size 12 bytes, gpio.o(i.Refresh_Delayed_DI))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = Refresh_Delayed_DI
</UL>
<BR>[Calls]<UL><LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delayed_DI
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_DI_Flag
</UL>

<P><STRONG><a name="[99]"></a>Refresh_Load_Flag</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, limit.o(i.Refresh_Load_Flag))
<BR><BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breaker_Check
</UL>

<P><STRONG><a name="[179]"></a>Refresh_Monitor_Data</STRONG> (Thumb, 450 bytes, Stack size 8 bytes, net.o(i.Refresh_Monitor_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Refresh_Monitor_Data
</UL>
<BR>[Called By]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Monitor
</UL>

<P><STRONG><a name="[108]"></a>Refresh_Own_Data</STRONG> (Thumb, 118 bytes, Stack size 8 bytes, optical.o(i.Refresh_Own_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Refresh_Own_Data
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Online_Data
</UL>

<P><STRONG><a name="[175]"></a>Refresh_Parameters</STRONG> (Thumb, 1916 bytes, Stack size 48 bytes, para.o(i.Refresh_Parameters))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = Refresh_Parameters &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Mode
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Start
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Wave_Option
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Limit_Option
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PSS2A_T_K
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Byte_To_Float
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_ROM
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_List
</UL>

<P><STRONG><a name="[1c3]"></a>Refresh_eBrake_Flag</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, dido.o(i.Refresh_eBrake_Flag))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Refresh_eBrake_Flag
</UL>
<BR>[Calls]<UL><LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;S104_Check
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;S102_Check
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;S101_Check
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_DO_Flag
</UL>

<P><STRONG><a name="[bc]"></a>Reset_CL1606a</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, cl1606_spi.o(i.Reset_CL1606a))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Reset_CL1606a
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Func
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_AD
</UL>

<P><STRONG><a name="[bd]"></a>Reset_CL1606b</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, cl1606_spi.o(i.Reset_CL1606b))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Reset_CL1606b
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Func
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_AD
</UL>

<P><STRONG><a name="[1bc]"></a>Running_Signal</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dido.o(i.Running_Signal))
<BR><BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_DO_Flag
</UL>

<P><STRONG><a name="[193]"></a>Rx_Buffer</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, optical_fiber.o(i.Rx_Buffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Rx_Buffer
</UL>
<BR>[Calls]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_State
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_Comm
</UL>

<P><STRONG><a name="[1c7]"></a>S101_Check</STRONG> (Thumb, 214 bytes, Stack size 0 bytes, dido.o(i.S101_Check))
<BR><BR>[Called By]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_eBrake_Flag
</UL>

<P><STRONG><a name="[1c8]"></a>S102_Check</STRONG> (Thumb, 290 bytes, Stack size 0 bytes, dido.o(i.S102_Check))
<BR><BR>[Called By]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_eBrake_Flag
</UL>

<P><STRONG><a name="[1c9]"></a>S104_Check</STRONG> (Thumb, 366 bytes, Stack size 0 bytes, dido.o(i.S104_Check))
<BR><BR>[Called By]<UL><LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_eBrake_Flag
</UL>

<P><STRONG><a name="[1cb]"></a>SPI0_DMA_Tx_Config</STRONG> (Thumb, 152 bytes, Stack size 80 bytes, spi.o(i.SPI0_DMA_Tx_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = SPI0_DMA_Tx_Config &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_single_data_para_struct_init
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_single_data_mode_init
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Init
</UL>

<P><STRONG><a name="[1d0]"></a>SPI0_Init</STRONG> (Thumb, 290 bytes, Stack size 32 bytes, spi.o(i.SPI0_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = SPI0_Init &rArr; SPI0_DMA_Tx_Config &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_enable
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_init
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;syscfg_exti_line_config
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_deinit
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_dma_enable
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_DMA_Tx_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_work_ctrl
</UL>

<P><STRONG><a name="[12c]"></a>SPI0_Input_Init</STRONG> (Thumb, 116 bytes, Stack size 8 bytes, spi.o(i.SPI0_Input_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SPI0_Input_Init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_interrupt_enable
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exti_init
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;syscfg_exti_line_config
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_work_ctrl
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Hardware
</UL>

<P><STRONG><a name="[e9]"></a>SPI0_Rx</STRONG> (Thumb, 166 bytes, Stack size 24 bytes, spi.o(i.SPI0_Rx))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SPI0_Rx &rArr; dma_flag_get
</UL>
<BR>[Calls]<UL><LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_transfer_number_get
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI4_IRQHandler
</UL>

<P><STRONG><a name="[18e]"></a>SPI1_DMA_Config</STRONG> (Thumb, 184 bytes, Stack size 80 bytes, internet.o(i.SPI1_DMA_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = SPI1_DMA_Config &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_single_data_para_struct_init
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_single_data_mode_init
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_dma_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Network_Init
</UL>

<P><STRONG><a name="[18d]"></a>SPI2_DMA_Config</STRONG> (Thumb, 184 bytes, Stack size 80 bytes, internet.o(i.SPI2_DMA_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = SPI2_DMA_Config &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_single_data_para_struct_init
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_single_data_mode_init
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_deinit
<LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_subperipheral_select
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_enable
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_channel_disable
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_dma_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Network_Init
</UL>

<P><STRONG><a name="[1da]"></a>SPI_ReadWriteByte</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, ad57x4.o(i.SPI_ReadWriteByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SPI_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_bit_get
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_IO
</UL>
<BR>[Called By]<UL><LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WriteByte
</UL>

<P><STRONG><a name="[1dc]"></a>SPI_WriteByte</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, ad57x4.o(i.SPI_WriteByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SPI_WriteByte &rArr; SPI_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WriteToSPI_IO
</UL>

<P><STRONG><a name="[1dd]"></a>SPI_work_ctrl</STRONG> (Thumb, 124 bytes, Stack size 8 bytes, spi.o(i.SPI_work_ctrl))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = SPI_work_ctrl &rArr; SPI0_Init &rArr; SPI0_DMA_Tx_Config &rArr; dma_single_data_mode_init
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Input_Init
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_check
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[128]"></a>SRAM_Init</STRONG> (Thumb, 1670 bytes, Stack size 96 bytes, nandflash_sram.o(i.SRAM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = SRAM_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_norsram_init
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_norsram_enable
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exmc_norsram_consecutive_clock_config
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Hardware
</UL>

<P><STRONG><a name="[17c]"></a>SRAM_ReadBuffer</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, nandflash_sram.o(i.SRAM_ReadBuffer))
<BR><BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Upload_Wave_Info
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Ref
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Record_Check
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Upload_Wave
</UL>

<P><STRONG><a name="[13f]"></a>SRAM_WriteBuffer</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, nandflash_sram.o(i.SRAM_WriteBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SRAM_WriteBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Wave_Info
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Store_Data
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Pre_Tx_Wave
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Wave_Info
</UL>

<P><STRONG><a name="[1e3]"></a>STMFlash_Read_Byte</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, flash.o(i.STMFlash_Read_Byte))
<BR><BR>[Called By]<UL><LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFlash_Write_Byte
</UL>

<P><STRONG><a name="[1ad]"></a>STMFlash_Read_Bytes</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, flash.o(i.STMFlash_Read_Bytes))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = STMFlash_Read_Bytes
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_ROM
</UL>

<P><STRONG><a name="[1df]"></a>STMFlash_Write_Byte</STRONG> (Thumb, 174 bytes, Stack size 40 bytes, flash.o(i.STMFlash_Write_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = STMFlash_Write_Byte &rArr; fmc_byte_program &rArr; fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_unlock
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_erase
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_lock
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_flag_clear
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_byte_program
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFlash_Read_Byte
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSector
</UL>
<BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_ROM
</UL>

<P><STRONG><a name="[142]"></a>Scr_Exit_Check</STRONG> (Thumb, 324 bytes, Stack size 24 bytes, limit.o(i.Scr_Exit_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Scr_Exit_Check &rArr; Work_Mode_V
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Wave_12_Flag
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_V
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Ifd
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Limit_Check
</UL>

<P><STRONG><a name="[194]"></a>Search_Command</STRONG> (Thumb, 470 bytes, Stack size 32 bytes, optical.o(i.Search_Command))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = Search_Command &rArr; Get_Scr_Optical &rArr; Clear_Comm_Cycle &rArr; Get_Scr_Ifd &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Scr_Optical
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DeEx_Optical
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Bak_Ch_Optical
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Collect_Scr_Sensor_ID
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_Comm
</UL>

<P><STRONG><a name="[1ef]"></a>Sec_Syn_Check</STRONG> (Thumb, 98 bytes, Stack size 0 bytes, syn.o(i.Sec_Syn_Check))
<BR><BR>[Called By]<UL><LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Syn_Check
</UL>

<P><STRONG><a name="[199]"></a>SelectSynSource</STRONG> (Thumb, 378 bytes, Stack size 0 bytes, optical.o(i.SelectSynSource))
<BR><BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_Comm
</UL>

<P><STRONG><a name="[117]"></a>Set_Limit_Option</STRONG> (Thumb, 652 bytes, Stack size 0 bytes, para.o(i.Set_Limit_Option))
<BR><BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Coef
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_Parameters
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Coef
</UL>

<P><STRONG><a name="[154]"></a>Set_RTC</STRONG> (Thumb, 116 bytes, Stack size 48 bytes, para.o(i.Set_RTC))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = Set_RTC &rArr; rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_current_time_get
</UL>
<BR>[Called By]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Coef
</UL>

<P><STRONG><a name="[da]"></a>Set_Start_Flag</STRONG> (Thumb, 132 bytes, Stack size 8 bytes, start.o(i.Set_Start_Flag))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Set_Start_Flag
</UL>
<BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Clear_Over_V_Flag
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI_Start
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Test
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Ctrl
</UL>

<P><STRONG><a name="[92]"></a>Set_Wave_12_Flag</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, limit.o(i.Set_Wave_12_Flag))
<BR><BR>[Called By]<UL><LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stop_Machine_Check
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VF_Check
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Short_Circuit_Check
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Scr_Exit_Check
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Over_V_Check
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Over_I_Check
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Over_Ex_Check
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breaker_Check
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Bak_PT_Check
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PT_Check
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Under_Ex_U
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Over_Q_U
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;E_Brake_On_Check
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;E_Brake_Off_Check
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AVR
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Mode
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Ex
</UL>

<P><STRONG><a name="[118]"></a>Set_Wave_Option</STRONG> (Thumb, 376 bytes, Stack size 0 bytes, para.o(i.Set_Wave_Option))
<BR><BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Coef
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_Parameters
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Coef
</UL>

<P><STRONG><a name="[81]"></a>Shift_Data</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, ai.o(i.Shift_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Shift_Data
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AI
</UL>

<P><STRONG><a name="[143]"></a>Short_Circuit_Check</STRONG> (Thumb, 212 bytes, Stack size 4 bytes, limit.o(i.Short_Circuit_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Short_Circuit_Check
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Wave_12_Flag
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Limit_Check
</UL>

<P><STRONG><a name="[177]"></a>Socket_Correct_Addr</STRONG> (Thumb, 278 bytes, Stack size 40 bytes, internet.o(i.Socket_Correct_Addr))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Socket_Correct_Addr &rArr; Write_W5500 &rArr; spi_rw_byte_spi2
</UL>
<BR>[Calls]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_W5500
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Byte_W5500
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_Byte_W5500
</UL>
<BR>[Called By]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Mode_Select
</UL>

<P><STRONG><a name="[176]"></a>Socket_Mode</STRONG> (Thumb, 162 bytes, Stack size 24 bytes, internet.o(i.Socket_Mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = Socket_Mode &rArr; Write_Byte_W5500 &rArr; spi_rw_byte_spi2
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Byte_W5500
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_Byte_W5500
</UL>
<BR>[Called By]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Mode_Select
</UL>

<P><STRONG><a name="[188]"></a>Soft_Delay</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, internet.o(i.Soft_Delay))
<BR><BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Network_Init
</UL>

<P><STRONG><a name="[1e7]"></a>Soft_Start</STRONG> (Thumb, 280 bytes, Stack size 40 bytes, start.o(i.Soft_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = Soft_Start &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Check
</UL>

<P><STRONG><a name="[1b8]"></a>Soft_Wait</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, ref.o(i.Soft_Wait))
<BR><BR>[Called By]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Step_Up
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Step_Down
</UL>

<P><STRONG><a name="[190]"></a>Standby_Check</STRONG> (Thumb, 256 bytes, Stack size 0 bytes, limit.o(i.Standby_Check))
<BR><BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Online_Check
</UL>

<P><STRONG><a name="[1e9]"></a>Start_Check</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, start.o(i.Start_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = Start_Check &rArr; Soft_Start &rArr; __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Mode
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_Start
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Ex
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1ea]"></a>Start_Mode</STRONG> (Thumb, 582 bytes, Stack size 8 bytes, start.o(i.Start_Mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = Start_Mode &rArr; Work_Mode_Alpha
</UL>
<BR>[Calls]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Alpha
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Wave_12_Flag
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_V
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Ifd
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Clear_Over_V_Flag
</UL>
<BR>[Called By]<UL><LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Check
</UL>

<P><STRONG><a name="[14a]"></a>Start_PT_Check</STRONG> (Thumb, 128 bytes, Stack size 0 bytes, limit.o(i.Start_PT_Check))
<BR><BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Limit_Check
</UL>

<P><STRONG><a name="[165]"></a>Start_Test</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, start.o(i.Start_Test))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Start_Test &rArr; Set_Start_Flag
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_V
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Start_Flag
</UL>
<BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Ctrl
</UL>

<P><STRONG><a name="[164]"></a>Step_Check</STRONG> (Thumb, 126 bytes, Stack size 72 bytes, command.o(i.Step_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = Step_Check &rArr; __aeabi_memcpy4
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Ctrl
</UL>

<P><STRONG><a name="[1ec]"></a>Stop_Machine_Check</STRONG> (Thumb, 304 bytes, Stack size 8 bytes, start.o(i.Stop_Machine_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Stop_Machine_Check &rArr; Init_Flag &rArr; Init_Mode &rArr; Work_Mode_V
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Flag
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Wave_12_Flag
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_V
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Clear_Over_V_Flag
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c2]"></a>Store_Data</STRONG> (Thumb, 402 bytes, Stack size 8 bytes, syn.o(i.Store_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Store_Data &rArr; Float_To_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Float_To_Byte
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SRAM_WriteBuffer
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DiDo
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Collect_Data
</UL>

<P><STRONG><a name="[163]"></a>Switch_Operating_Mode</STRONG> (Thumb, 148 bytes, Stack size 16 bytes, command.o(i.Switch_Operating_Mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = Switch_Operating_Mode &rArr; Work_Mode_Cos &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Alpha
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_V
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Q
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Ifd
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Cos
</UL>
<BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Ctrl
</UL>

<P><STRONG><a name="[162]"></a>Switch_Start_Mode</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, command.o(i.Switch_Start_Mode))
<BR><BR>[Called By]<UL><LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Ctrl
</UL>

<P><STRONG><a name="[1ee]"></a>Syn_Check</STRONG> (Thumb, 104 bytes, Stack size 12 bytes, syn.o(i.Syn_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = Syn_Check
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Clear_Syn_Error
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sec_Syn_Check
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Primary_Syn_Check
</UL>
<BR>[Called By]<UL><LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC4
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC2
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC1
</UL>

<P><STRONG><a name="[12e]"></a>Syn_Pulse_Init</STRONG> (Thumb, 184 bytes, Stack size 8 bytes, syn.o(i.Syn_Pulse_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Syn_Pulse_Init &rArr; Init_Wave_Info &rArr; SRAM_WriteBuffer
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Clear_Syn_Error
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Wave_Info
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Hardware
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Low_F_Check
</UL>

<P><STRONG><a name="[65]"></a>SystemInit</STRONG> (Thumb, 168 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SystemInit &rArr; system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(.text)
</UL>
<P><STRONG><a name="[1f2]"></a>TIM1_Init</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, tim.o(i.TIM1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = TIM1_Init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_struct_para_init
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_enable
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_internal_clock_config
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_flag_clear
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_auto_reload_shadow_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMx_Init
</UL>

<P><STRONG><a name="[1fb]"></a>TIM4_Init</STRONG> (Thumb, 276 bytes, Stack size 40 bytes, tim.o(i.TIM4_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = TIM4_Init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_struct_para_init
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_enable
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_input_capture_config
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_init
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_flag_clear
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_enable
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_struct_para_init
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_state_config
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_output_config
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_input_struct_para_init
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_autoreload_value_config
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_auto_reload_shadow_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMx_Init
</UL>

<P><STRONG><a name="[202]"></a>TIM5_CC1</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, tim.o(i.TIM5_CC1))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = TIM5_CC1 &rArr; Cal_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Syn_Check
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Phase_Difference
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER4_IRQHandler
</UL>

<P><STRONG><a name="[206]"></a>TIM5_CC2</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, tim.o(i.TIM5_CC2))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = TIM5_CC2 &rArr; Cal_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Syn_Check
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Phase_Difference
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER4_IRQHandler
</UL>

<P><STRONG><a name="[207]"></a>TIM5_CC3</STRONG> (Thumb, 82 bytes, Stack size 8 bytes, tim.o(i.TIM5_CC3))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = TIM5_CC3 &rArr; Net_Rx_Tx &rArr; Net_Command &rArr; Net_Coef &rArr; Read_ROM &rArr; Refresh_Parameters &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AI
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_Comm
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Rx_Tx
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Upload_Wave
</UL>
<BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER4_IRQHandler
</UL>

<P><STRONG><a name="[209]"></a>TIM5_CC4</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, tim.o(i.TIM5_CC4))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = TIM5_CC4 &rArr; Cal_Period
</UL>
<BR>[Calls]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Syn_Check
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Phase_Difference
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_Period
</UL>
<BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER4_IRQHandler
</UL>

<P><STRONG><a name="[28]"></a>TIMER1_IRQHandler</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, tim.o(i.TIMER1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = TIMER1_IRQHandler &rArr; Net_Rx_Tx &rArr; Net_Command &rArr; Net_Coef &rArr; Read_ROM &rArr; Refresh_Parameters &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_clear
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AI
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_Comm
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Rx_Tx
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Upload_Wave
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>TIMER4_IRQHandler</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, tim.o(i.TIMER4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = TIMER4_IRQHandler &rArr; TIM5_CC3 &rArr; Net_Rx_Tx &rArr; Net_Command &rArr; Net_Coef &rArr; Read_ROM &rArr; Refresh_Parameters &rArr; __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_interrupt_flag_get
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC4
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC3
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC2
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC1
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_gd32f450.o(RESET)
</UL>
<P><STRONG><a name="[12d]"></a>TIMx_Init</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, tim.o(i.TIMx_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = TIMx_Init &rArr; TIM4_Init &rArr; nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_Init
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Hardware
</UL>

<P><STRONG><a name="[197]"></a>Tx_Buffer</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, optical_fiber.o(i.Tx_Buffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Tx_Buffer
</UL>
<BR>[Called By]<UL><LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_Comm
</UL>

<P><STRONG><a name="[95]"></a>U_To_Alpha</STRONG> (Thumb, 212 bytes, Stack size 48 bytes, pid.o(i.U_To_Alpha))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = U_To_Alpha &rArr; __hardfp_acos &rArr; sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FCR
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AVR
</UL>

<P><STRONG><a name="[ba]"></a>Upload_AD_Const</STRONG> (Thumb, 1258 bytes, Stack size 48 bytes, command.o(i.Upload_AD_Const))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = Upload_AD_Const &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_VI
</UL>

<P><STRONG><a name="[20d]"></a>Upload_Ex_Data</STRONG> (Thumb, 162 bytes, Stack size 24 bytes, command.o(i.Upload_Ex_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Upload_Ex_Data &rArr; CRC16
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[208]"></a>Upload_Wave</STRONG> (Thumb, 202 bytes, Stack size 120 bytes, syn.o(i.Upload_Wave))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = Upload_Wave &rArr; CRC16
</UL>
<BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SRAM_ReadBuffer
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_IRQHandler
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC3
</UL>

<P><STRONG><a name="[17b]"></a>Upload_Wave_Info</STRONG> (Thumb, 160 bytes, Stack size 40 bytes, syn.o(i.Upload_Wave_Info))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = Upload_Wave_Info &rArr; CRC16
</UL>
<BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SRAM_ReadBuffer
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Record_Check
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Pre_Tx_Wave
</UL>

<P><STRONG><a name="[145]"></a>VF_Check</STRONG> (Thumb, 496 bytes, Stack size 80 bytes, limit.o(i.VF_Check))
<BR><BR>[Stack]<UL><LI>Max Depth = 344<LI>Call Chain = VF_Check &rArr; __hardfp_pow &rArr; sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Wave_12_Flag
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Limit_Check
</UL>

<P><STRONG><a name="[1b4]"></a>V_Ref_Q</STRONG> (Thumb, 452 bytes, Stack size 16 bytes, ref.o(i.V_Ref_Q))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = V_Ref_Q &rArr; Work_Mode_V
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_V
</UL>
<BR>[Called By]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Com
</UL>

<P><STRONG><a name="[1a1]"></a>Wash_Out</STRONG> (Thumb, 44 bytes, Stack size 4 bytes, pss.o(i.Wash_Out))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Wash_Out
</UL>
<BR>[Calls]<UL><LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Filter
</UL>
<BR>[Called By]<UL><LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PSS2A
</UL>

<P><STRONG><a name="[1a9]"></a>Wash_Out_T</STRONG> (Thumb, 52 bytes, Stack size 4 bytes, pss4b.o(i.Wash_Out_T))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Wash_Out_T
</UL>
<BR>[Calls]<UL><LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Filter_T
</UL>
<BR>[Called By]<UL><LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PSS4B_Input
</UL>

<P><STRONG><a name="[c1]"></a>Wave_Info</STRONG> (Thumb, 184 bytes, Stack size 32 bytes, syn.o(i.Wave_Info))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = Wave_Info &rArr; Get_RTC &rArr; rtc_current_time_get
</UL>
<BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_RTC
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SRAM_WriteBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Collect_Data
</UL>

<P><STRONG><a name="[1eb]"></a>Work_Mode_Alpha</STRONG> (Thumb, 102 bytes, Stack size 4 bytes, mode.o(i.Work_Mode_Alpha))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Work_Mode_Alpha
</UL>
<BR>[Calls]<UL><LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mode_Save
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Clear_Start_Flag
</UL>
<BR>[Called By]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Switch_Operating_Mode
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Mode
</UL>

<P><STRONG><a name="[103]"></a>Work_Mode_Cos</STRONG> (Thumb, 242 bytes, Stack size 32 bytes, mode.o(i.Work_Mode_Cos))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = Work_Mode_Cos &rArr; __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mode_Save
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DI3
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Switch_Operating_Mode
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Tracking
</UL>

<P><STRONG><a name="[d8]"></a>Work_Mode_Ifd</STRONG> (Thumb, 228 bytes, Stack size 4 bytes, mode.o(i.Work_Mode_Ifd))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Work_Mode_Ifd
</UL>
<BR>[Calls]<UL><LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mode_Save
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Clear_Start_Flag
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Mode
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Scr_Exit_Check
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI_FCR
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PT_Check
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Switch_Operating_Mode
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;E_Brake_On_Check
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Mode
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Tracking
</UL>

<P><STRONG><a name="[102]"></a>Work_Mode_Q</STRONG> (Thumb, 130 bytes, Stack size 4 bytes, mode.o(i.Work_Mode_Q))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Work_Mode_Q
</UL>
<BR>[Calls]<UL><LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mode_Save
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_DI3
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Switch_Operating_Mode
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Tracking
</UL>

<P><STRONG><a name="[9a]"></a>Work_Mode_V</STRONG> (Thumb, 230 bytes, Stack size 4 bytes, mode.o(i.Work_Mode_V))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Work_Mode_V
</UL>
<BR>[Calls]<UL><LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mode_Save
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Clear_Start_Flag
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Mode
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stop_Machine_Check
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Scr_Exit_Check
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Breaker_Check
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DI_AVR
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PT_Check
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Switch_Operating_Mode
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Test
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;E_Brake_Off_Check
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Mode
<LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;V_Ref_Q
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Tracking
</UL>

<P><STRONG><a name="[185]"></a>Write_ROM</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, para.o(i.Write_ROM))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = Write_ROM &rArr; STMFlash_Write_Byte &rArr; fmc_byte_program &rArr; fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFlash_Write_Byte
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Parameters
</UL>
<BR>[Called By]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Write_ROM
</UL>

<P><STRONG><a name="[215]"></a>__ARM_fpclassify</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_fpclassify
</UL>
<BR>[Called By]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
</UL>

<P><STRONG><a name="[20b]"></a>__hardfp_acos</STRONG> (Thumb, 738 bytes, Stack size 72 bytes, acos.o(i.__hardfp_acos))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = __hardfp_acos &rArr; sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;U_To_Alpha
</UL>

<P><STRONG><a name="[b0]"></a>__hardfp_atan</STRONG> (Thumb, 622 bytes, Stack size 48 bytes, atan.o(i.__hardfp_atan))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = __hardfp_atan &rArr; __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dneg
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_Atan
</UL>

<P><STRONG><a name="[f6]"></a>__hardfp_cos</STRONG> (Thumb, 180 bytes, Stack size 32 bytes, cos.o(i.__hardfp_cos))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = __hardfp_cos &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dneg
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Alpha
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Coef
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_Parameters
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Coef
</UL>

<P><STRONG><a name="[19f]"></a>__hardfp_pow</STRONG> (Thumb, 3072 bytes, Stack size 192 bytes, pow.o(i.__hardfp_pow))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = __hardfp_pow &rArr; sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dneg
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VF_Check
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Over_I_Check
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Over_Ex_Check
</UL>

<P><STRONG><a name="[1e8]"></a>__hardfp_sin</STRONG> (Thumb, 180 bytes, Stack size 32 bytes, sin.o(i.__hardfp_sin))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = __hardfp_sin &rArr; __ieee754_rem_pio2 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dneg
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_Start
</UL>

<P><STRONG><a name="[b5]"></a>__hardfp_sqrt</STRONG> (Thumb, 122 bytes, Stack size 32 bytes, sqrt.o(i.__hardfp_sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = __hardfp_sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RMS_Ifd
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RMS
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_VI
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Cos
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Coef
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Scr_Ifd
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_Parameters
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Coef
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Under_Ex_U
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Step_Up
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Step_Down
</UL>

<P><STRONG><a name="[219]"></a>__ieee754_rem_pio2</STRONG> (Thumb, 938 bytes, Stack size 120 bytes, rred.o(i.__ieee754_rem_pio2))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = __ieee754_rem_pio2 &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fabs
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dneg
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>

<P><STRONG><a name="[21b]"></a>__kernel_cos</STRONG> (Thumb, 322 bytes, Stack size 64 bytes, cos_i.o(i.__kernel_cos))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = __kernel_cos &rArr; __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>

<P><STRONG><a name="[212]"></a>__kernel_poly</STRONG> (Thumb, 248 bytes, Stack size 24 bytes, poly.o(i.__kernel_poly))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
</UL>

<P><STRONG><a name="[21a]"></a>__kernel_sin</STRONG> (Thumb, 280 bytes, Stack size 72 bytes, sin_i.o(i.__kernel_sin))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = __kernel_sin &rArr; __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
</UL>

<P><STRONG><a name="[21d]"></a>__mathlib_dbl_divzero</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_divzero))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __mathlib_dbl_divzero &rArr; __aeabi_ddiv
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[210]"></a>__mathlib_dbl_infnan</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_infnan))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __mathlib_dbl_infnan &rArr; __aeabi_dadd
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
</UL>

<P><STRONG><a name="[21c]"></a>__mathlib_dbl_infnan2</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_infnan2))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __mathlib_dbl_infnan2 &rArr; __aeabi_dadd
</UL>
<BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[211]"></a>__mathlib_dbl_invalid</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_invalid))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __mathlib_dbl_invalid &rArr; __aeabi_ddiv
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
</UL>

<P><STRONG><a name="[21e]"></a>__mathlib_dbl_overflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_overflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __mathlib_dbl_overflow &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[216]"></a>__mathlib_dbl_underflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_underflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __mathlib_dbl_underflow &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
</UL>

<P><STRONG><a name="[222]"></a>build_ready_data</STRONG> (Thumb, 1956 bytes, Stack size 24 bytes, spi.o(i.build_ready_data))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = build_ready_data &rArr; sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Float_To_Byte
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_check
</UL>

<P><STRONG><a name="[eb]"></a>dma_channel_disable</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_channel_disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_channel_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI2_DMA_Config
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_DMA_Config
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Init
<LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI4_IRQHandler
</UL>

<P><STRONG><a name="[ec]"></a>dma_channel_enable</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_channel_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_channel_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI2_DMA_Config
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_DMA_Config
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_tx_process
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_rx_process
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_DMA_Tx_Config
<LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI4_IRQHandler
</UL>

<P><STRONG><a name="[1ce]"></a>dma_channel_subperipheral_select</STRONG> (Thumb, 38 bytes, Stack size 12 bytes, gd32f4xx_dma.o(i.dma_channel_subperipheral_select))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = dma_channel_subperipheral_select
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI2_DMA_Config
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_DMA_Config
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_DMA_Tx_Config
</UL>

<P><STRONG><a name="[1cc]"></a>dma_deinit</STRONG> (Thumb, 164 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_deinit
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI2_DMA_Config
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_DMA_Config
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_DMA_Tx_Config
</UL>

<P><STRONG><a name="[16f]"></a>dma_flag_clear</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_flag_clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_flag_clear
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_tx_process
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_rx_process
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_check
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Rx
</UL>

<P><STRONG><a name="[16d]"></a>dma_flag_get</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, gd32f4xx_dma.o(i.dma_flag_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dma_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_tx_process
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_rx_process
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_check
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Rx
</UL>

<P><STRONG><a name="[1cd]"></a>dma_single_data_mode_init</STRONG> (Thumb, 340 bytes, Stack size 16 bytes, gd32f4xx_dma.o(i.dma_single_data_mode_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = dma_single_data_mode_init
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI2_DMA_Config
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_DMA_Config
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_DMA_Tx_Config
</UL>

<P><STRONG><a name="[1cf]"></a>dma_single_data_para_struct_init</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, gd32f4xx_dma.o(i.dma_single_data_para_struct_init))
<BR><BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI2_DMA_Config
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_DMA_Config
<LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_DMA_Tx_Config
</UL>

<P><STRONG><a name="[1d6]"></a>dma_transfer_number_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_dma.o(i.dma_transfer_number_get))
<BR><BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Rx
</UL>

<P><STRONG><a name="[19c]"></a>exmc_norsram_consecutive_clock_config</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(i.exmc_norsram_consecutive_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_fiber_FSMC_Init
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SRAM_Init
</UL>

<P><STRONG><a name="[19d]"></a>exmc_norsram_enable</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_exmc.o(i.exmc_norsram_enable))
<BR><BR>[Called By]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_fiber_FSMC_Init
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SRAM_Init
</UL>

<P><STRONG><a name="[19b]"></a>exmc_norsram_init</STRONG> (Thumb, 224 bytes, Stack size 12 bytes, gd32f4xx_exmc.o(i.exmc_norsram_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = exmc_norsram_init
</UL>
<BR>[Called By]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_fiber_FSMC_Init
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SRAM_Init
</UL>

<P><STRONG><a name="[1d2]"></a>exti_init</STRONG> (Thumb, 184 bytes, Stack size 8 bytes, gd32f4xx_exti.o(i.exti_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = exti_init
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Input_Init
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Init
</UL>

<P><STRONG><a name="[1d3]"></a>exti_interrupt_enable</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_exti.o(i.exti_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Input_Init
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Init
</UL>

<P><STRONG><a name="[e7]"></a>exti_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_exti.o(i.exti_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI4_IRQHandler
</UL>

<P><STRONG><a name="[e6]"></a>exti_interrupt_flag_get</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_exti.o(i.exti_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI4_IRQHandler
</UL>

<P><STRONG><a name="[217]"></a>fabs</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, fabs.o(i.fabs))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fabs
</UL>
<BR>[Called By]<UL><LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
</UL>

<P><STRONG><a name="[1e5]"></a>fmc_byte_program</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, gd32f4xx_fmc.o(i.fmc_byte_program))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = fmc_byte_program &rArr; fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_ready_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFlash_Write_Byte
</UL>

<P><STRONG><a name="[1e1]"></a>fmc_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFlash_Write_Byte
</UL>

<P><STRONG><a name="[1e6]"></a>fmc_lock</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_lock))
<BR><BR>[Called By]<UL><LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFlash_Write_Byte
</UL>

<P><STRONG><a name="[223]"></a>fmc_ready_wait</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, gd32f4xx_fmc.o(i.fmc_ready_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_state_get
</UL>
<BR>[Called By]<UL><LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_sector_erase
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_byte_program
</UL>

<P><STRONG><a name="[1e4]"></a>fmc_sector_erase</STRONG> (Thumb, 90 bytes, Stack size 12 bytes, gd32f4xx_fmc.o(i.fmc_sector_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = fmc_sector_erase &rArr; fmc_ready_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_ready_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFlash_Write_Byte
</UL>

<P><STRONG><a name="[224]"></a>fmc_state_get</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_state_get))
<BR><BR>[Called By]<UL><LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fmc_ready_wait
</UL>

<P><STRONG><a name="[1e0]"></a>fmc_unlock</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_fmc.o(i.fmc_unlock))
<BR><BR>[Called By]<UL><LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;STMFlash_Write_Byte
</UL>

<P><STRONG><a name="[a3]"></a>gpio_af_set</STRONG> (Thumb, 94 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_af_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_af_set
</UL>
<BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_PIN_Init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_PIN_Init
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI5_For_W5200_Init
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI4_For_W5200_Init
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_fiber_FSMC_Init
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SRAM_Init
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Init
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_Init
</UL>

<P><STRONG><a name="[9d]"></a>gpio_bit_reset</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_reset))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Reset_CL1606b
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Reset_CL1606a
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_Start_Convst
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_Init
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_Start_Convst
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_Init
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_Read_Reg
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_PIN_Init
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_Read_Reg
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_PIN_Init
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Network_Init
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_ReadWriteByte
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WriteToSPI_IO
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_fiber_Init
</UL>

<P><STRONG><a name="[9e]"></a>gpio_bit_set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_bit_set))
<BR><BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Reset_CL1606b
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Reset_CL1606a
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_Start_Convst
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_Init
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_Start_Convst
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_Init
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_Read_Reg
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_PIN_Init
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_Read_Reg
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_PIN_Init
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W5200U2_RST_Off
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W5200U1_RST_Off
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_ReadWriteByte
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;WriteToSPI_IO
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_IoInit
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_fiber_Init
</UL>

<P><STRONG><a name="[e8]"></a>gpio_input_bit_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_gpio.o(i.gpio_input_bit_get))
<BR><BR>[Called By]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_DIP_Switch
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_AD2_Sample
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_AD1_Sample
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_ReadWriteByte
<LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI4_IRQHandler
</UL>

<P><STRONG><a name="[a1]"></a>gpio_mode_set</STRONG> (Thumb, 78 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_mode_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_mode_set
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DIP_Switch_Init
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_PIN_Init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_PIN_Init
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W5200U2_RST_Init
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W5200U1_RST_Init
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI5_For_W5200_Init
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI4_For_W5200_Init
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_IoInit
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_fiber_FSMC_Init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGA_RST_Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGA_DONE_Init
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SRAM_Init
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Input_Init
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Init
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_Init
</UL>

<P><STRONG><a name="[a2]"></a>gpio_output_options_set</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, gd32f4xx_gpio.o(i.gpio_output_options_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_output_options_set
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DIP_Switch_Init
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_PIN_Init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_PIN_Init
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W5200U2_RST_Init
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W5200U1_RST_Init
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI5_For_W5200_Init
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI4_For_W5200_Init
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_IoInit
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_fiber_FSMC_Init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGA_RST_Init
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGA_DONE_Init
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SRAM_Init
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Input_Init
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Init
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_Init
</UL>

<P><STRONG><a name="[8f]"></a>iPID</STRONG> (Thumb, 492 bytes, Stack size 12 bytes, pid.o(i.iPID))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = iPID
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AVR
</UL>

<P><STRONG><a name="[10f]"></a>intToChar</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, para.o(i.intToChar))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Parameters
</UL>

<P><STRONG><a name="[72]"></a>main</STRONG> (Thumb, 172 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = main &rArr; Limit_Check &rArr; VF_Check &rArr; __hardfp_pow &rArr; sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_Avg
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Upload_Ex_Data
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Stop_Machine_Check
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Start_Check
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_work_ctrl
<LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Com
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PSS4B
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PSS2A
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Write_ROM
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Once
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Hardware
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Alpha
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Collect_Data
<LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Boot_Delay
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Limit_Check
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DIDO
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[1d4]"></a>nvic_irq_enable</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, gd32f4xx_misc.o(i.nvic_irq_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = nvic_irq_enable
</UL>
<BR>[Calls]<UL><LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_priority_group_set
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Input_Init
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Init
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_Init
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_Init
</UL>

<P><STRONG><a name="[226]"></a>nvic_priority_group_set</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_misc.o(i.nvic_priority_group_set))
<BR><BR>[Called By]<UL><LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nvic_irq_enable
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[136]"></a>pmu_backup_write_enable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_pmu.o(i.pmu_backup_write_enable))
<BR><BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_RTC
</UL>

<P><STRONG><a name="[13c]"></a>rcu_all_reset_flag_clear</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_RTC
</UL>

<P><STRONG><a name="[228]"></a>rcu_flag_get</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_osci_stab_wait
</UL>

<P><STRONG><a name="[137]"></a>rcu_osci_on</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_osci_on))
<BR><BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_RTC
</UL>

<P><STRONG><a name="[138]"></a>rcu_osci_stab_wait</STRONG> (Thumb, 342 bytes, Stack size 20 bytes, gd32f4xx_rcu.o(i.rcu_osci_stab_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = rcu_osci_stab_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_flag_get
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_RTC
</UL>

<P><STRONG><a name="[a0]"></a>rcu_periph_clock_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_clock_enable))
<BR><BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DIP_Switch_Init
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_PIN_Init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_PIN_Init
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI2_DMA_Config
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_DMA_Config
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W5200U2_RST_Init
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W5200U1_RST_Init
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI5_For_W5200_Init
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI4_For_W5200_Init
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_IoInit
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Optical_fiber_FSMC_Init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FPGA_RST_Init
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SRAM_Init
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Input_Init
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_RTC
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Init
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_Init
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_Init
</UL>

<P><STRONG><a name="[22d]"></a>rcu_periph_reset_disable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_disable))
<BR><BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_deinit
</UL>

<P><STRONG><a name="[22c]"></a>rcu_periph_reset_enable</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_periph_reset_enable))
<BR><BR>[Called By]<UL><LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_deinit
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_deinit
</UL>

<P><STRONG><a name="[139]"></a>rcu_rtc_clock_config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, gd32f4xx_rcu.o(i.rcu_rtc_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_RTC
</UL>

<P><STRONG><a name="[110]"></a>rtc_current_time_get</STRONG> (Thumb, 96 bytes, Stack size 12 bytes, gd32f4xx_rtc.o(i.rtc_current_time_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = rtc_current_time_get
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_RTC
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_RTC
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_RTC
</UL>

<P><STRONG><a name="[13d]"></a>rtc_flag_clear</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_RTC
</UL>

<P><STRONG><a name="[13b]"></a>rtc_init</STRONG> (Thumb, 190 bytes, Stack size 20 bytes, gd32f4xx_rtc.o(i.rtc_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = rtc_init &rArr; rtc_register_sync_wait
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_register_sync_wait
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init_mode_exit
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init_mode_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_RTC
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_RTC
</UL>

<P><STRONG><a name="[229]"></a>rtc_init_mode_enter</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, gd32f4xx_rtc.o(i.rtc_init_mode_enter))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rtc_init_mode_enter
</UL>
<BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[22a]"></a>rtc_init_mode_exit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gd32f4xx_rtc.o(i.rtc_init_mode_exit))
<BR><BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
</UL>

<P><STRONG><a name="[13a]"></a>rtc_register_sync_wait</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, gd32f4xx_rtc.o(i.rtc_register_sync_wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = rtc_register_sync_wait
</UL>
<BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rtc_init
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_RTC
</UL>

<P><STRONG><a name="[1de]"></a>spi_check</STRONG> (Thumb, 122 bytes, Stack size 8 bytes, spi.o(i.spi_check))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = spi_check &rArr; build_ready_data &rArr; sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_get
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dma_flag_clear
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_slave_tx
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;build_ready_data
</UL>
<BR>[Called By]<UL><LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_work_ctrl
</UL>

<P><STRONG><a name="[a5]"></a>spi_crc_polynomial_set</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_crc_polynomial_set))
<BR><BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_PIN_Init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_PIN_Init
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI5_For_W5200_Init
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI4_For_W5200_Init
</UL>

<P><STRONG><a name="[170]"></a>spi_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_disable))
<BR><BR>[Called By]<UL><LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_tx_process
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_rx_process
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_W5500
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Byte_W5500
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI5_For_W5200_Init
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI4_For_W5200_Init
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_W5500
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_Byte_W5500
</UL>

<P><STRONG><a name="[171]"></a>spi_dma_disable</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_dma_disable))
<BR><BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_rx_process
</UL>

<P><STRONG><a name="[16c]"></a>spi_dma_enable</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_dma_enable))
<BR><BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI2_DMA_Config
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_DMA_Config
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_rx_process
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Init
</UL>

<P><STRONG><a name="[a6]"></a>spi_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_enable))
<BR><BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_PIN_Init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_PIN_Init
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_tx_process
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_rx_process
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_W5500
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Byte_W5500
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_W5500
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_Byte_W5500
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Init
</UL>

<P><STRONG><a name="[1d8]"></a>spi_i2s_data_receive</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_data_receive))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI4_RW_One_Byte
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI3_RW_One_Byte
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_rw_byte_spi2
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_rw_byte_spi1
</UL>

<P><STRONG><a name="[1d7]"></a>spi_i2s_data_transmit</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_data_transmit))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI4_RW_One_Byte
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI3_RW_One_Byte
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_rw_byte_spi2
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_rw_byte_spi1
</UL>

<P><STRONG><a name="[1d5]"></a>spi_i2s_deinit</STRONG> (Thumb, 162 bytes, Stack size 8 bytes, gd32f4xx_spi.o(i.spi_i2s_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = spi_i2s_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI5_For_W5200_Init
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI4_For_W5200_Init
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Init
</UL>

<P><STRONG><a name="[16e]"></a>spi_i2s_flag_get</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_i2s_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI4_RW_One_Byte
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI3_RW_One_Byte
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_tx_process
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_rx_process
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_rw_byte_spi2
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_rw_byte_spi1
</UL>

<P><STRONG><a name="[a4]"></a>spi_init</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_init))
<BR><BR>[Called By]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_PIN_Init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_PIN_Init
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI5_For_W5200_Init
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI4_For_W5200_Init
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Init
</UL>

<P><STRONG><a name="[1d9]"></a>spi_nss_output_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_spi.o(i.spi_nss_output_enable))
<BR><BR>[Called By]<UL><LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI5_For_W5200_Init
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI4_For_W5200_Init
</UL>

<P><STRONG><a name="[22b]"></a>spi_slave_tx</STRONG> (Thumb, 56 bytes, Stack size 20 bytes, spi.o(i.spi_slave_tx))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = spi_slave_tx
</UL>
<BR>[Called By]<UL><LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_check
</UL>

<P><STRONG><a name="[214]"></a>sqrt</STRONG> (Thumb, 110 bytes, Stack size 32 bytes, sqrt.o(i.sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;build_ready_data
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
</UL>

<P><STRONG><a name="[1d1]"></a>syscfg_exti_line_config</STRONG> (Thumb, 166 bytes, Stack size 16 bytes, gd32f4xx_syscfg.o(i.syscfg_exti_line_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = syscfg_exti_line_config
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Input_Init
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI0_Init
</UL>

<P><STRONG><a name="[1f8]"></a>timer_auto_reload_shadow_disable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable))
<BR><BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_Init
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_Init
</UL>

<P><STRONG><a name="[201]"></a>timer_autoreload_value_config</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_autoreload_value_config))
<BR><BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_Init
</UL>

<P><STRONG><a name="[22f]"></a>timer_channel_input_capture_prescaler_config</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config))
<BR><BR>[Called By]<UL><LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_input_capture_config
</UL>

<P><STRONG><a name="[1fc]"></a>timer_channel_input_struct_para_init</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_input_struct_para_init))
<BR><BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_Init
</UL>

<P><STRONG><a name="[1ff]"></a>timer_channel_output_config</STRONG> (Thumb, 484 bytes, Stack size 8 bytes, gd32f4xx_timer.o(i.timer_channel_output_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = timer_channel_output_config
</UL>
<BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_Init
</UL>

<P><STRONG><a name="[200]"></a>timer_channel_output_state_config</STRONG> (Thumb, 92 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_state_config))
<BR><BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_Init
</UL>

<P><STRONG><a name="[1fe]"></a>timer_channel_output_struct_para_init</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_channel_output_struct_para_init))
<BR><BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_Init
</UL>

<P><STRONG><a name="[1f3]"></a>timer_deinit</STRONG> (Thumb, 374 bytes, Stack size 8 bytes, gd32f4xx_timer.o(i.timer_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = timer_deinit
</UL>
<BR>[Calls]<UL><LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_enable
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_reset_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_Init
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_Init
</UL>

<P><STRONG><a name="[1fa]"></a>timer_enable</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_enable))
<BR><BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_Init
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_Init
</UL>

<P><STRONG><a name="[1f7]"></a>timer_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_Init
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_Init
</UL>

<P><STRONG><a name="[1f6]"></a>timer_init</STRONG> (Thumb, 122 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_init))
<BR><BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_Init
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_Init
</UL>

<P><STRONG><a name="[1fd]"></a>timer_input_capture_config</STRONG> (Thumb, 326 bytes, Stack size 16 bytes, gd32f4xx_timer.o(i.timer_input_capture_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = timer_input_capture_config
</UL>
<BR>[Calls]<UL><LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;timer_channel_input_capture_prescaler_config
</UL>
<BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_Init
</UL>

<P><STRONG><a name="[1f4]"></a>timer_internal_clock_config</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_internal_clock_config))
<BR><BR>[Called By]<UL><LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_Init
</UL>

<P><STRONG><a name="[1f9]"></a>timer_interrupt_enable</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_enable))
<BR><BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_Init
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_Init
</UL>

<P><STRONG><a name="[203]"></a>timer_interrupt_flag_clear</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_flag_clear))
<BR><BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER1_IRQHandler
<LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC4
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC3
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC2
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_CC1
</UL>

<P><STRONG><a name="[20a]"></a>timer_interrupt_flag_get</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_interrupt_flag_get))
<BR><BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMER4_IRQHandler
</UL>

<P><STRONG><a name="[1f5]"></a>timer_struct_para_init</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gd32f4xx_timer.o(i.timer_struct_para_init))
<BR><BR>[Called By]<UL><LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_Init
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM1_Init
</UL>

<P><STRONG><a name="[218]"></a>__aeabi_dneg</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, basic.o(x$fpl$basic))
<BR><BR>[Called By]<UL><LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sin
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_cos
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
</UL>

<P><STRONG><a name="[277]"></a>_dneg</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, basic.o(x$fpl$basic), UNUSED)

<P><STRONG><a name="[278]"></a>__aeabi_fneg</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, basic.o(x$fpl$basic), UNUSED)

<P><STRONG><a name="[279]"></a>_fneg</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, basic.o(x$fpl$basic), UNUSED)

<P><STRONG><a name="[27a]"></a>_dabs</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, basic.o(x$fpl$basic), UNUSED)

<P><STRONG><a name="[27b]"></a>_fabs</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, basic.o(x$fpl$basic), UNUSED)

<P><STRONG><a name="[85]"></a>__aeabi_d2f</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, d2f.o(x$fpl$d2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RMS_Ifd
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RMS
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_Inv_Phase
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_VI
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_Omiga
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_Atan
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AI
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Alpha
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VF_Check
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Over_I_Check
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Over_Ex_Check
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Cos
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Coef
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Scr_Optical
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Scr_Ifd
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Bak_Ch_Optical
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_Parameters
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Coef
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;U_To_Alpha
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Under_Ex_U
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Over_Q_U
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_Start
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Step_Up
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Step_Down
</UL>

<P><STRONG><a name="[230]"></a>_d2f</STRONG> (Thumb, 98 bytes, Stack size 32 bytes, d2f.o(x$fpl$d2f), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[b1]"></a>__aeabi_dadd</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, daddsub_clz.o(x$fpl$dadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_Inv_Phase
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_Omiga
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_Atan
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VF_Check
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Over_Q_U
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
</UL>

<P><STRONG><a name="[233]"></a>_dadd</STRONG> (Thumb, 332 bytes, Stack size 16 bytes, daddsub_clz.o(x$fpl$dadd), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub1
</UL>

<P><STRONG><a name="[236]"></a>__fpl_dcheck_NaN1</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, dcheck1.o(x$fpl$dcheck1))
<BR><BR>[Calls]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<BR>[Called By]<UL><LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>

<P><STRONG><a name="[23b]"></a>__fpl_dcmp_Inf</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, dcmpi.o(x$fpl$dcmpinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmple
</UL>

<P><STRONG><a name="[f9]"></a>__aeabi_ddiv</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, ddiv.o(x$fpl$ddiv))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RMS
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VF_Check
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Over_Ex_Check
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Upload_AD_Const
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Scr_Optical
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Bak_Ch_Optical
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;U_To_Alpha
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Step_Up
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Step_Down
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_divzero
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
</UL>

<P><STRONG><a name="[238]"></a>_ddiv</STRONG> (Thumb, 552 bytes, Stack size 32 bytes, ddiv.o(x$fpl$ddiv), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[20c]"></a>__aeabi_d2iz</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dfix.o(x$fpl$dfix))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_d2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Upload_AD_Const
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
</UL>

<P><STRONG><a name="[239]"></a>_dfix</STRONG> (Thumb, 94 bytes, Stack size 32 bytes, dfix.o(x$fpl$dfix), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[83]"></a>__aeabi_i2d</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dflt))
<BR><BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RMS
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AI
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Upload_AD_Const
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Scr_Optical
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[27c]"></a>_dflt</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dflt), UNUSED)

<P><STRONG><a name="[f8]"></a>__aeabi_ui2d</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dfltu))
<BR><BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Upload_AD_Const
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Bak_Ch_Optical
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
</UL>

<P><STRONG><a name="[27d]"></a>_dfltu</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dfltu), UNUSED)

<P><STRONG><a name="[f4]"></a>__aeabi_cdcmple</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dleqf.o(x$fpl$dleqf))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_cdcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Alpha
</UL>

<P><STRONG><a name="[23a]"></a>_dcmple</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, dleqf.o(x$fpl$dleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcmp_Inf
</UL>

<P><STRONG><a name="[23e]"></a>__fpl_dcmple_InfNaN</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, dleqf.o(x$fpl$dleqf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drcmple
</UL>

<P><STRONG><a name="[84]"></a>__aeabi_dmul</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RMS_Ifd
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RMS
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AI
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VF_Check
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Over_Ex_Check
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Upload_AD_Const
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Coef
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_Parameters
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Coef
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;U_To_Alpha
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Over_Q_U
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_Start
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
</UL>

<P><STRONG><a name="[23c]"></a>_dmul</STRONG> (Thumb, 332 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[232]"></a>__fpl_dnaninf</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, dnaninf.o(x$fpl$dnaninf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __fpl_dnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmple
<LI><a href="#[239]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dfix
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ddiv
<LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2f
</UL>

<P><STRONG><a name="[235]"></a>__fpl_dretinf</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dretinf.o(x$fpl$dretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ddiv
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
</UL>

<P><STRONG><a name="[21f]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, drleqf.o(x$fpl$drleqf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[23d]"></a>_drcmple</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, drleqf.o(x$fpl$drleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcmple_InfNaN
</UL>

<P><STRONG><a name="[213]"></a>__aeabi_drsub</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, daddsub_clz.o(x$fpl$drsb))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_drsub
</UL>
<BR>[Called By]<UL><LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
</UL>

<P><STRONG><a name="[23f]"></a>_drsb</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, daddsub_clz.o(x$fpl$drsb), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub1
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd1
</UL>

<P><STRONG><a name="[221]"></a>_dsqrt</STRONG> (Thumb, 404 bytes, Stack size 24 bytes, dsqrt_umaal.o(x$fpl$dsqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrt
</UL>

<P><STRONG><a name="[10a]"></a>__aeabi_dsub</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, daddsub_clz.o(x$fpl$dsub))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VF_Check
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Over_I_Check
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Over_Ex_Check
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Over_Q_U
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_sin
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ieee754_rem_pio2
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_cos
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_acos
</UL>

<P><STRONG><a name="[241]"></a>_dsub</STRONG> (Thumb, 464 bytes, Stack size 32 bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd1
</UL>

<P><STRONG><a name="[82]"></a>__aeabi_f2d</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RMS_Ifd
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RMS
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_Inv_Phase
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_VI
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_Omiga
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Cal_Atan
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AI
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Alpha
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VF_Check
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Over_I_Check
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Over_Ex_Check
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Work_Mode_Cos
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Coef
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Scr_Ifd
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Refresh_Parameters
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Coef
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;build_ready_data
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;U_To_Alpha
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Under_Ex_U
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Over_Q_U
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Soft_Start
<LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Step_Up
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ref_Step_Down
</UL>

<P><STRONG><a name="[242]"></a>_f2d</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
</UL>

<P><STRONG><a name="[243]"></a>__fpl_fnaninf</STRONG> (Thumb, 140 bytes, Stack size 8 bytes, fnaninf.o(x$fpl$fnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
</UL>

<P><STRONG><a name="[6c]"></a>_fp_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[27e]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[27f]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[231]"></a>__fpl_fretinf</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fretinf.o(x$fpl$fretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2f
</UL>

<P><STRONG><a name="[237]"></a>__fpl_return_NaN</STRONG> (Thumb, 100 bytes, Stack size 0 bytes, retnan.o(x$fpl$retnan))
<BR><BR>[Calls]<UL><LI><a href="#[244]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_cmpreturn
</UL>
<BR>[Called By]<UL><LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcheck_NaN1
</UL>

<P><STRONG><a name="[220]"></a>__ARM_scalbn</STRONG> (Thumb, 92 bytes, Stack size 0 bytes, scalbn.o(x$fpl$scalbn))
<BR><BR>[Calls]<UL><LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcheck_NaN1
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_pow
</UL>

<P><STRONG><a name="[244]"></a>__fpl_cmpreturn</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, trapv.o(x$fpl$trapveneer))
<BR><BR>[Called By]<UL><LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[22e]"></a>system_clock_200m_25m_hxtal</STRONG> (Thumb, 240 bytes, Stack size 0 bytes, system_gd32f4xx.o(i.system_clock_200m_25m_hxtal))
<BR><BR>[Called By]<UL><LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_config
</UL>

<P><STRONG><a name="[1f1]"></a>system_clock_config</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, system_gd32f4xx.o(i.system_clock_config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = system_clock_config
</UL>
<BR>[Calls]<UL><LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_clock_200m_25m_hxtal
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[11e]"></a>SPI_IoInit</STRONG> (Thumb, 144 bytes, Stack size 8 bytes, ad57x4.o(i.SPI_IoInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = SPI_IoInit &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_DA
</UL>

<P><STRONG><a name="[c8]"></a>WriteToSPI_IO</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, ad57x4.o(i.WriteToSPI_IO))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = WriteToSPI_IO &rArr; SPI_WriteByte &rArr; SPI_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DA_N10_10V
</UL>

<P><STRONG><a name="[c6]"></a>Config_W5500</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, internet.o(i.Config_W5500))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = Config_W5500 &rArr; Write_Byte_W5500 &rArr; spi_rw_byte_spi2
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Byte_W5500
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Network_Init
</UL>

<P><STRONG><a name="[16b]"></a>Read_Byte_W5500</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, internet.o(i.Read_Byte_W5500))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Read_Byte_W5500 &rArr; spi_rw_byte_spi2
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_disable
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_rw_byte_spi2
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_rw_byte_spi1
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Socket_Mode
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Socket_Correct_Addr
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Tx
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Rx
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Interrupt_Process
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_rx_process
</UL>

<P><STRONG><a name="[17d]"></a>Read_W5500</STRONG> (Thumb, 136 bytes, Stack size 32 bytes, internet.o(i.Read_W5500))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Read_W5500 &rArr; spi_rw_byte_spi2
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_disable
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_W5100
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_rw_byte_spi2
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_rw_byte_spi1
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Rx
</UL>

<P><STRONG><a name="[187]"></a>SPI4_For_W5200_Init</STRONG> (Thumb, 232 bytes, Stack size 32 bytes, internet.o(i.SPI4_For_W5200_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = SPI4_For_W5200_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_nss_output_enable
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_deinit
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_disable
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_crc_polynomial_set
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Network_Init
</UL>

<P><STRONG><a name="[18b]"></a>SPI5_For_W5200_Init</STRONG> (Thumb, 250 bytes, Stack size 32 bytes, internet.o(i.SPI5_For_W5200_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = SPI5_For_W5200_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_nss_output_enable
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_deinit
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_disable
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_crc_polynomial_set
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Network_Init
</UL>

<P><STRONG><a name="[18a]"></a>W5200U1_RST_Init</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, internet.o(i.W5200U1_RST_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = W5200U1_RST_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W5200U1_RST_Off
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Network_Init
</UL>

<P><STRONG><a name="[18c]"></a>W5200U1_RST_Off</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, internet.o(i.W5200U1_RST_Off))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = W5200U1_RST_Off
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Network_Init
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W5200U1_RST_Init
</UL>

<P><STRONG><a name="[186]"></a>W5200U2_RST_Init</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, internet.o(i.W5200U2_RST_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = W5200U2_RST_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W5200U2_RST_Off
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Network_Init
</UL>

<P><STRONG><a name="[189]"></a>W5200U2_RST_Off</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, internet.o(i.W5200U2_RST_Off))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = W5200U2_RST_Off
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Network_Init
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W5200U2_RST_Init
</UL>

<P><STRONG><a name="[c7]"></a>Write_Byte_W5500</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, internet.o(i.Write_Byte_W5500))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Write_Byte_W5500 &rArr; spi_rw_byte_spi2
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_disable
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_W5100
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_rw_byte_spi2
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_rw_byte_spi1
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Socket_Mode
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Socket_Correct_Addr
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Network_Init
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Tx
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Rx
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Interrupt_Process
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_tx_process
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_DMA_rx_process
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Cycle
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Config_W5500
</UL>

<P><STRONG><a name="[182]"></a>Write_W5500</STRONG> (Thumb, 132 bytes, Stack size 32 bytes, internet.o(i.Write_W5500))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Write_W5500 &rArr; spi_rw_byte_spi2
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_disable
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_W5100
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_rw_byte_spi2
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_rw_byte_spi1
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Socket_Correct_Addr
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Network_Init
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Net_Tx
</UL>

<P><STRONG><a name="[1ab]"></a>spi_rw_byte_spi1</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, internet.o(i.spi_rw_byte_spi1))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = spi_rw_byte_spi1
</UL>
<BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_W5500
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Byte_W5500
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_W5500
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_Byte_W5500
</UL>

<P><STRONG><a name="[1ac]"></a>spi_rw_byte_spi2</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, internet.o(i.spi_rw_byte_spi2))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = spi_rw_byte_spi2
</UL>
<BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_W5500
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Write_Byte_W5500
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_W5500
<LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_Byte_W5500
</UL>

<P><STRONG><a name="[9c]"></a>CL1606a_PIN_Init</STRONG> (Thumb, 288 bytes, Stack size 32 bytes, cl1606_spi.o(i.CL1606a_PIN_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = CL1606a_PIN_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_crc_polynomial_set
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_Init
</UL>

<P><STRONG><a name="[a7]"></a>CL1606a_Read_Reg</STRONG> (Thumb, 62 bytes, Stack size 24 bytes, cl1606_spi.o(i.CL1606a_Read_Reg))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = CL1606a_Read_Reg &rArr; SPI3_RW_One_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI3_RW_One_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_AD1_Sample
</UL>

<P><STRONG><a name="[ab]"></a>CL1606b_PIN_Init</STRONG> (Thumb, 308 bytes, Stack size 32 bytes, cl1606_spi.o(i.CL1606b_PIN_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = CL1606b_PIN_Init &rArr; gpio_output_options_set
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_output_options_set
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_mode_set
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_af_set
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcu_periph_clock_enable
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_init
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_enable
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_crc_polynomial_set
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_Init
</UL>

<P><STRONG><a name="[ac]"></a>CL1606b_Read_Reg</STRONG> (Thumb, 62 bytes, Stack size 24 bytes, cl1606_spi.o(i.CL1606b_Read_Reg))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = CL1606b_Read_Reg &rArr; SPI4_RW_One_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_set
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_bit_reset
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI4_RW_One_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Read_AD2_Sample
</UL>

<P><STRONG><a name="[a8]"></a>SPI3_RW_One_Byte</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, cl1606_spi.o(i.SPI3_RW_One_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SPI3_RW_One_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606a_Read_Reg
</UL>

<P><STRONG><a name="[ad]"></a>SPI4_RW_One_Byte</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, cl1606_spi.o(i.SPI4_RW_One_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SPI4_RW_One_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_flag_get
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_transmit
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_i2s_data_receive
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CL1606b_Read_Reg
</UL>

<P><STRONG><a name="[225]"></a>Boot_Delay</STRONG> (Thumb, 40 bytes, Stack size 12 bytes, main.o(i.Boot_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = Boot_Delay
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ea]"></a>Process_Recv_Frame</STRONG> (Thumb, 62 bytes, Stack size 24 bytes, spi.o(i.Process_Recv_Frame))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Process_Recv_Frame &rArr; CRC16
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CRC16
</UL>
<BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI4_IRQHandler
</UL>

<P><STRONG><a name="[240]"></a>_dadd1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, daddsub_clz.o(x$fpl$dadd), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub
<LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drsb
</UL>

<P><STRONG><a name="[234]"></a>_dsub1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drsb
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
