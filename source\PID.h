/********************************************************************
 * COPYRIGHT --  
 ********************************************************************
 * Program: Ex
 * File: PID.h
 * Author: Yu Xiang
 * Created: April 24, 2018
 ********************************************************************
 * Implementation of program Ex
 ********************************************************************/
#ifndef __PID_H
#define __PID_H

extern float Uv, Uqi, Uqc, Uii, <PERSON>ic, Uifd, Upss, Uifd_min;

void Init_PID(void);

void Get_Alpha(void);

#endif
//===========================================================================
// End of file.
//===========================================================================

