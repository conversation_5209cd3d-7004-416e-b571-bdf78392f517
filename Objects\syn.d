.\objects\syn.o: source\syn.c
.\objects\syn.o: source\pub.h
.\objects\syn.o: source\const.h
.\objects\syn.o: source\nandflash_sram.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx.h
.\objects\syn.o: .\stdDriver\inc\core_cm4.h
.\objects\syn.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\syn.o: .\stdDriver\inc\core_cmInstr.h
.\objects\syn.o: .\stdDriver\inc\core_cmFunc.h
.\objects\syn.o: .\stdDriver\inc\core_cm4_simd.h
.\objects\syn.o: .\stdDriver\inc\system_gd32f4xx.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_libopt.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_rcu.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_adc.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_can.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_crc.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_ctc.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_dac.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_dbg.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_dci.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_dma.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_exti.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_fmc.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_fwdgt.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_gpio.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_syscfg.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_i2c.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_iref.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_pmu.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_rtc.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_sdio.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_spi.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_timer.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_trng.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_usart.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_wwdgt.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_misc.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_enet.h
.\objects\syn.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_exmc.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_ipa.h
.\objects\syn.o: .\stdDriver\inc\gd32f4xx_tli.h
.\objects\syn.o: source\internet.h
.\objects\syn.o: source\optical.h
.\objects\syn.o: source\para.h
.\objects\syn.o: source\command.h
