/********************************************************************
*pub.h																*
*ȫ�ֱ���������														*
********************************************************************/ 

extern unsigned char EE_Data[1000];//�̻����� 
extern unsigned char Scr_Comm_Data[128];//�����·��Ĺ��ʹ�����
extern unsigned char Scr_Sensor_ID[120];//���ʹ��ϴ��Ĵ�����ID 
extern unsigned char Dis_Comm_Data[128];//�����·�����Ź����� 

//limit.c
//����������־
extern unsigned char Over_Ex_Enabled_Flag;
extern unsigned char Scr_Exit_Enabled_Flag;
extern unsigned char Short_Circuit_Enabled_Flag;
extern unsigned char Under_Ex_Enabled_Flag;
extern unsigned char Over_Q_Enabled_Flag;
extern unsigned char PT_Break_Enabled_Flag;
extern unsigned char Over_V_Enabled_Flag;
extern unsigned char VF_Enabled_Flag;

extern unsigned char E_Brake_Enabled_Flag;
extern unsigned char Bak_PT_Enabled_Flag;
extern unsigned char Syn_Err_Enabled_Flag;
extern unsigned char No_Load_Over_Ief_Enabled_Flag;
extern unsigned char Homis_Enabled_Flag;
extern unsigned char Extra_DI_Enabled_Flag;
extern unsigned char Breaker_Check_Enabled_Flag;
extern unsigned char Bak_Ex_Enabled_Flag;

extern unsigned char Over_I_Enabled_Flag;//���ӵ��������־;
extern unsigned char Limit_17_Enabled_Flag;
extern unsigned char Ifd_AI_Flag;	//���ڹ�ֱ�Ӳɼ�Ifd
extern unsigned char Ifd_Min_Enabled_Flag;
extern unsigned char Ief_Enabled_Flag;
extern unsigned char IRIG_B_Enabled_Flag;
extern unsigned char Limit_22_Enabled_Flag;
extern unsigned char Limit_23_Enabled_Flag;
extern unsigned char S101_Inverse_DI_Flag;
extern unsigned char Breaker_Inverse_DI_Flag;

extern unsigned char Limit_24_Enabled_Flag;
extern unsigned char Limit_25_Enabled_Flag;
extern unsigned char Limit_26_Enabled_Flag;
extern unsigned char Limit_27_Enabled_Flag;
extern unsigned char Limit_28_Enabled_Flag;
extern unsigned char Limit_29_Enabled_Flag;
extern unsigned char Limit_30_Enabled_Flag;
extern unsigned char Limit_31_Enabled_Flag;
extern unsigned char Scr_Comm_Err_Flag;		//�й��ʹ����ͨ�Ź���
extern unsigned char Bak_Scr_Comm_Err_Flag;	//��һͨ���й��ʹ����ͨ�Ź���
extern unsigned char Record_Flag;//���ݼ�¼��־, 10ms��ѭ����һ�μ�¼һ������
extern unsigned char Optical_DIP_Error_Flag; //���˶˿�������id��ƥ��
extern unsigned char Ifd_Min_Wave_Flag;	//���������Զ�¼����־

extern short int nWaveData[50];//��������:16�ֽڿ�����, 17��ģ����, 4������  

//�Զ�¼����־
extern unsigned char Start_Wave_Flag;		//�����Զ�¼��
extern unsigned char Auto_Ex_Fail_Wave_Flag;//����ʧ���Զ�¼��
extern unsigned char Stop_Wave_Flag;		//����Զ�¼��
extern unsigned char E_Brake_On_Wave_Flag;	//�����ƶ�Ͷ���Զ�¼��
extern unsigned char E_Brake_Off_Wave_Flag;	//�����ƶ��˳��Զ�¼��
extern unsigned char Breaker_On_Wave_Flag;	//�����Զ�¼��
extern unsigned char Breaker_Off_Wave_Flag;	//�����Զ�¼��
extern unsigned char PT_Break_Wave_Flag;	//PT�����Զ�¼��
extern unsigned char Syn_Break_Wave_Flag;	//���ʹ�ͬ�������Զ�¼��
extern unsigned char Over_Q_Wave_Flag;		//���޹��Զ�¼��
extern unsigned char Under_Ex_Wave_Flag;	//Ƿ���Զ�¼��
extern unsigned char Load_Limit_Wave_Flag;	//���������Զ�¼��
extern unsigned char VF_Wave_Flag;			//���������Զ�¼��
extern unsigned char Short_Circuit_Wave_Flag;//��·�Զ�¼��
extern unsigned char Over_Ex_Wave_Flag;		//ǿ���Զ�¼��
extern unsigned char Over_V_Wave_Flag;		//���ع�ѹ�Զ�¼��
extern unsigned char Over_I_Wave_Flag;		//���ӵ��������Զ�¼��
extern unsigned char Bak_PT_Wave_Flag;		//��һͨ��PT��ֵƫ���Զ�¼��

extern unsigned char Net_Send_Flag;//���緢������
extern unsigned char PSS_Active_Flag;//PSS�����Ч��־
extern unsigned char DO_Lock_Flag;//�ϵ翪��������־
extern short int DO_Lock_Delay;//�ϵ翪��������ʱ

//Over Q
extern float Over_Q_0;//�й�Ϊ0ʱ��Ӧ���޹�����
extern float Over_Q_05;//�й�Ϊ0.5ʱ��Ӧ���޹�����
extern float Over_Q_1;//�й�ΪPQ_Pmaxʱ��Ӧ���޹�����
extern short Over_Q_Count;	//���������������ֹ��޹������ÿ���һ�κ��޹���ʽ�ĸ���ֵ
extern unsigned char Over_Q_Flag;	//������־
extern unsigned char Over_Q_Event_Flag;	//�����¼���־�������ϴ�����λ�����¼���¼
extern short int Over_Q_Ref_Down_Delay;	//���޹�ʱ�ڲ��Զ����ŵ���ʱ����֤������ַ��ӵ�ճճ

//Ifd min
extern float Ifd_Min_Limit;//��������
extern unsigned char Ifd_Min_Flag;//��������

//Breaker check
extern unsigned char Breaker_Flag;	//��·����־��1����·����
extern unsigned char Breaker_Old_Flag;//��·��ԭ��״̬��־�������ж϶�·��״̬�ı仯
extern unsigned char Pulse_Enabled_Flag;//����������־
extern unsigned char Pulse_Bak_Flag;//����ͨ������������־
extern unsigned char Load_Flag;//������־����·���ϻ��ص�������0.05ʱ�жϲ���
extern int Load_Count;//���ӵ�������0.1�Ժ���ʱ���ò�����־

//short circuit
extern unsigned char Short_Circuit_Flag;//��·��־��V<0.9��I>1.05ʱ�ж϶�·
extern int Short_Circuit_Count; //��·������ʱ���������ӳ�0.4s����

//PT break check
extern unsigned char PT_Break_Flag;//PT���߱�־
extern unsigned char Bak_PT_Break_Flag;//��һͨ��PT���߱�־, ͨ��ͨ�Ż�ȡ
extern unsigned char Bak_PT_High_Flag;//����PT��ֵ�ϸ߱�־, ��ʱ������ִ��ͨ���л�
extern int PT_Break_Count; //PT�����ӳټ���������ʱһ�����������ж�
extern unsigned char PT_A_Flag;
extern unsigned char PT_B_Flag;
extern unsigned char PT_C_Flag;
extern short int PT_Online_Delay;//PT���ߺ���ʱ�˳�����
extern unsigned char PT_Switch_Flag;//PT���������ͨ���л���־
extern unsigned char PT_Unbalance_Flag;//PT�����ֵ�������־
extern short int PT_Unbalance_Delay;//PT�����ֵ��������ʱ
extern unsigned char CT_Unbalance_Flag;//CT�����ֵ�������־
extern short int CT_Unbalance_Delay;//CT�����ֵ��������ʱ
extern short int Bak_PT_Delay;//����PT��ֵ���ϴ�ʱ���л���ʱ

//Over I
extern unsigned char Over_I_Flag;//���ӵ��������־
extern unsigned char Over_I_Ind_Flag;//���ӵ��������־(����)
extern unsigned char Over_I_Cap_Flag;//���ӵ��������־(����)
extern unsigned char Over_I_Q_Flag;//���ӹ������޹����ʽ����������ʱתΪ���޹����ʿ��Ƶı�־
extern int Over_I_Delay;//���ӵ����������Ƶ���ʱ

extern unsigned char Over_Efd_Flag;//ת�ӹ�ѹ��־

//VF check
extern unsigned char VF_Flag;//��/�ձ�־
extern unsigned char VF_Event_Flag;//��/���¼���־��������λ���¼���¼
extern short int VF_Count;		//VF������ʱ�˳�������
extern unsigned char Alpha_VF_Flag;//��/�����ƴ�����ͣ����־
extern float Kvf;//��/������ϵ��
extern float Fvf_H;//�������Ʒ�Χ��Ƶ������
extern float Fvf_L;//�������Ʒ�Χ��Ƶ������
extern float VF_k;	//�������Ʒ�ʱ��ϵ��
extern float VF_n;	//�������Ʒ�ʱ��ָ��
extern float VF_Heat;//�������Ʒ�ʱ����������
extern float VF_Ref;	//�������Ƹ���ֵ, ���뾹����

extern unsigned char Start_Ex_Event_Flag;//�����¼���¼����־
extern unsigned char Ref_Up_Event_Flag;//�����¼���־
extern unsigned char Ref_Down_Event_Flag;//�����¼���־
extern unsigned char Over_V_Event_Flag;//���ع�ѹ�¼���־
extern unsigned char Ref_Up_Bak_Flag;	//�ڲ�������
extern unsigned char Ref_Down_Bak_Flag;	//�ڲ�������

//Over Ex
extern unsigned char Over_Ex_Flag;//������־
extern unsigned char Over_Ex_Event_Flag;//�����¼���־��������λ���¼���¼
extern unsigned char Over_Ex_Old_Flag;//��һ�������ڵ�ǿ����־�����ڹ���¼��
extern float Over_Ex_Count;//����ʱ����������

extern float Over_Ex_k;//ǿ����ʱ������
extern float Over_Ex_n;//ǿ����ʱ�޳˷�
extern float Over_Ex_Ifd_Ref;//ǿ�����Ƶ����ŵ�������ֵ
extern float Over_Ex_Ifd_Hold;//ǿ������ʱ���Ȼ�����ֵ
extern unsigned char Alpha_Over_Ex_Flag;//������If>Over_Ex_Ifd_23ʱ��ǿ����־

//Scr Exit
extern unsigned char Scr_Quit_Flag[5];//���ʹ��˳���־
extern unsigned char Scr_Fan_Err_Flag;//���ʹ���ͣ�˱�־
extern unsigned char Scr_Exit_Flag;

extern unsigned char Load_Limit_Flag;//�������Ʊ�־
extern unsigned char Ifd_Max_Limit_Flag;//˲ʱ������ŵ�����־
//extern int Scr_Exit_Num;	//���ʹ��˳�������
extern int Scr_Exit_Delay;	//���ڻָ�If_max����ʱ������
extern float Ifd_Max_Limit;//������ŵ���
extern float Scr_Exit_Ifd_1;//�˳�1̨���ʹ�ʱ��Ӧ�����ŵ���
extern float Scr_Exit_Ifd_2;//�˳�2̨���ʹ�ʱ��Ӧ�����ŵ���
extern float Scr_Exit_Ifd_3;//�˳�3̨���ʹ�ʱ��Ӧ�����ŵ���

//Under Ex
extern unsigned char Under_Ex_Flag;//Ƿ����־
extern unsigned char Under_Ex_Old_Flag;//��һ�������ڵ�Ƿ����־�����ڹ���¼��
extern unsigned char Under_Ex_Event_Flag;//Ƿ���¼���־��������λ���¼���¼
extern float Under_Ex_0;
extern float Under_Ex_25;
extern float Under_Ex_50;
extern float Under_Ex_75;
extern float Under_Ex_1;
extern float Xq;
extern short int Under_Ex_Ref_Up_Delay;	//Ƿ��ʱ�ڲ��Զ����ŵ���ʱ����֤������ַ��ӵ�ճճ
extern float Kx;//Ƿ��������

//Over V
extern unsigned char Over_V_Flag;//���ع�ѹ��־
// extern unsigned char Over_V_Event_Flag;//���ع�ѹ�¼���־��������λ���¼���¼ 
extern int Over_V_Count;//���ع�ѹ��־�Զ������ʱ 
extern float Vmax;//���ص�ѹ���ֵ
extern float Imax;//���ӵ���������ֵ

//Mode
extern unsigned char Work_Mode_V_Flag;//����˵�ѹ���з�ʽ
extern unsigned char Work_Mode_Ifd_Flag;//�����ŵ������з�ʽ
extern unsigned char Work_Mode_Q_Flag;//���޹��������з�ʽ
extern unsigned char Work_Mode_Cos_Flag;//�㹦���������з�ʽ
extern unsigned char Work_Mode_Alpha_Flag;//ƽ����з��?*/
extern unsigned char Old_Mode_V_Flag;//ԭ���з�ʽΪ���˵�ѹ��ʽ
extern unsigned char Old_Mode_Q_Flag;//ԭ���з�ʽΪ�޹����ʷ�ʽ
extern unsigned char Old_Mode_Ifd_Flag;//ԭ���з�ʽΪ���ŵ�����ʽ
extern unsigned char Old_Mode_Cos_Flag;//ԭ���з�ʽΪ����������ʽ
extern unsigned char Old_Mode_Alpha_Flag;//ԭ���з�ʽΪ���ƽǷ�ʽ
extern int Q_Ref_Delay;	//������ʱ��ÿ�����ٴ���ѭ������һ��

//status parameters
extern float Va;//a���ѹ
extern float Vb;//b���ѹ
extern float Vc;//c���ѹ
extern float Var;//a���ѹʵ��
extern float Vai;//a���ѹ�鲿
extern float Vbr;//b���ѹʵ��
extern float Vbi;//b���ѹ�鲿
extern float Vcr;//c���ѹʵ��
extern float Vci;//c���ѹ�鲿
extern float Vabc_Temp[2];//���˵�ѹ��ʱ��������ֵ�˲���
extern float Vabc;//���˵�ѹ
extern float Ia;//a�����
extern float Ib;//b�����
extern float Ic;//c�����
extern float Iar;//a�����ʵ��
extern float Iai;//a������鲿
extern float Ibr;//b�����ʵ��
extern float Ibi;//b������鲿
extern float Icr;//c�����ʵ��
extern float Ici;//c������鲿
extern float Iabc_Temp[2];//���ӵ�����ʱ��������ֵ�˲���
extern float Iabc;//���ӵ���
extern float Ifd;//���ŵ���
extern float Vfd;//���ŵ�ѹ
extern float Vs;//ϵͳ��ѹ
extern float Alpha;//���ƽ�
extern float F;//Ƶ��
extern float P;//�й�����
extern float Q;//�޹�����
extern float Ref;//����ֵ
extern float Ref_Ctrl;//���ϵ���ĸ���ֵ
extern long Period;//����
extern float Uout;//������
extern float Uout_max;	//����������, 1/Ef0
extern float Uout_min;	//����������, -1/Ef0
extern float Uout_av; //�������Ļ���ֵ
extern float dRef;	//����ƫ��
extern float dRef_Old;	//��һ�εĸ���ƫ����ڼ���΢��
extern float Vnoise;	//����������ֵ
extern float Vnoise_Old;	//��һ�β����İ���������ֵ

extern float Vav;//���˵�ѹƽ��ֵ
extern float Iav;//���ӵ���ƽ��ֵ�����ڵ����ƶ�
extern float Ifd_av;//���ŵ���ƽ��ֵ
extern float Vs_av;//ϵͳ��ѹƽ��ֵ������ϵͳ��ѹ����
extern float Fav;//Ƶ��ƽ��ֵ�����ڷ������Ƶ�
extern float Pav;//�й�����ƽ��ֵ������Ƿ���͹��޹�����
extern float Qav;//�޹�����ƽ��ֵ
extern long Period_av;//����ƽ��ֵ

extern float Avg_Ctrl_Coef;//Vfd, F, P�Ļ���ϵ��
extern float Avg_Coef;		//V, I, Ifd, Q�Ļ���ϵ��

extern float Vbak;			//ͨ�Ŵ����ı���ͨ���ĵ�ѹ
//**************************
//stop machine				
//**************************
extern unsigned char Alpha_Stop_Machine_Flag;//��ͣ����־
extern unsigned char Stop_Machine_Flag;//ͣ����
extern unsigned char Ch1_Flag;		//ͨ��1��־������Ϊ0ʱ��ͨ��Ϊͨ��1
extern unsigned char Ch_OK_Flag;	//ͨ��������־��Ϊ0ʱ��ʾ���ʺ��л�Ϊ����
extern unsigned char Bak_Ch_OK_Flag;//��һͨ��������־��Ϊ0ʱ��ʾ���ʺ��л�Ϊ����
extern unsigned char Online_Flag;//�������߱�־
extern unsigned char Ex_Fault_Flag;	//���Ź��ϱ�־
extern unsigned char Ex_Limit_Flag;	//�������Ʊ�־
extern unsigned char Online_Old_Flag;//��һ�������ڵı������߱�־ 
extern short int Channel_Switch_Count;//����ͨ���л�ʱ��ʱϨ������� 
extern short int Online_Delay;//����ͨ��ͬʱ������ʱ�л������ߵ���ʱ
extern short int Offline_Delay;//����ͨ��ͬʱ����ʱ�л������ߵ���ʱ
extern unsigned char Stop_Command;	//��������ͣ����
extern unsigned char Stop_Command_Old;	//ͣ������ǰ״̬����֤����ͣ�����ƽ���ֵ���������Ӧ
extern int Stop_Count;	//���ʱ�ޣ�����10s������
extern unsigned char Ch1_Online_Command;	//��������1��Ͷ��
extern unsigned char Ch2_Online_Command;	//��������2��Ͷ��
extern unsigned char Standby_Flag;	//���ʹ�����ʱ���ȱ���־ 
extern unsigned char Bak_Standby_Flag;	//���ʹ�����ʱ���ȱ���־ 
extern short int Standby_Delay;//����ͨ��ͬʱ���ȱ�ʱ�л����ȱ�����ʱ
extern short int Standby_Off_Delay;//����ͨ��ͬʱ�ȱ�ʱ�л������ȱ�����ʱ
extern unsigned char Scr_Online_Flag;//���ʹ����߱�־
extern unsigned char Online_Source[6];
extern short int Online_No;	//���߿���ģ����� 

//Calculate.c
//Cal_Ref_Q
extern unsigned char Main_Ex_Flag;//������־
extern unsigned char Bak_Ex_Flag;//������־

extern float Kvrefq;//����ϵ��
extern float Init_Ex_V;//�����˳���ѹ
extern float StartT;//�������̵�ʱ��(10ms)

//PID
extern float Kp_V;//��ѹ����
extern float Kp_Ifd;//���ŵ�������

extern float Ki_V;//��ѹ����ϵ��
extern float Ki_Ifd;//���ŵ�������ϵ��

extern float Kd_V;//��ѹ΢��ϵ��
extern float Kd_Ifd;//���ŵ���΢��ϵ��

extern float Kp;//����
extern float Ki;//����ϵ��
extern float Kd;//΢��ϵ��

//PSS2A
extern float PSS2A_X[12];//PSS2A�м�״̬�������ϵ��ʼ��
extern float PSS2A_Y[12];//PSS2A�м�״̬�������ϵ��ʼ��
extern float PSS2A_U;//PSS���

extern unsigned char PSS2A_In_Flag; //PSS�����־

extern float PSS2A_T1;
extern float PSS2A_T2;
extern float PSS2A_T3;
extern float PSS2A_T4;
extern float PSS2A_T5;
extern float PSS2A_T6;
extern float PSS2A_T7;
extern float PSS2A_T8;
extern float PSS2A_T9;
extern float PSS2A_Tw1;
extern float PSS2A_Tw2;
extern float PSS2A_Tw3;

extern float PSS2A_T1_K;
extern float PSS2A_T2_K;
extern float PSS2A_T3_K;
extern float PSS2A_T4_K;
extern float PSS2A_T5_K;
extern float PSS2A_T6_K;
extern float PSS2A_T7_K;
extern float PSS2A_T8_K;
extern float PSS2A_T9_K;
extern float PSS2A_Tw1_K;
extern float PSS2A_Tw2_K;
extern float PSS2A_Tw3_K;

extern float PSS2A_Ks1;
extern float PSS2A_Ks2;
extern float PSS2A_Ks3;
extern float PSS2A_Pin;	//Pav>PSS2A_Pinʱ��PSSͶ��
extern float PSS2A_Pout;	//Pav<PSS2A_Poutʱ��PSS�˳�
extern float PSS2A_Max;	//PSS����޷�
extern float Xq1;			//q��翹�����ڼ�����ٶ�

extern float Xd;
extern float Kda;			//DA����
extern float Ef0;			//�����������Ϊ���ۻ����ŵ�ѹ

extern unsigned char w_Init_Flag;	//��һ�μ�����ٶȵı�־���д˱�־�����㹦��
extern float Omiga_av;
extern float Omiga;		//���ٶȣ�����PSS���ٶȷ�֧����
extern float Omiga_Old;	//��һ���˲���Ľ��ٶȣ�����PSS���ٶȷ�֧����
extern float Omiga_Source;	//��һ�μ���õ��Ľ��ٶȣ������˲�
extern int PSS2A_Delay;
extern int Noise_Index;	//�����������
extern float Sita_dt;		//���ڼ�����ٶȵ�ʱ���
extern float Sita;			//���ڽ��ٶȼ���Ĺ���
extern float Sita_Old;		//��һ�μ���Ĺ���
extern float dSita;		//���Ǳ仯

extern float Knoise;//�����Ŵ�����PSS�����ã�

//PSS
extern unsigned char PSS_Lock_Flag;//PSS������־

extern float Ref_Step;//����ֵ����
extern float Ref_Step_Up_Adder;//��������ʱ���ӵĸ���ֵ�ܺ�
extern float Ref_Step_Down_Adder;//��������ʱ��С�ĸ���ֵ�ܺ�
extern float Ref_Step_Limit;//����������ʱ�������ڵĸ���ֵ��ֵ
extern float Ref_Max;//����ͨ�����Ŵﵽ�ĸ���ֵ���ֵ
extern float Ref_Min;//����ͨ�����Ŵﵽ�ĸ���ֵ��Сֵ
extern float Ref_Cos;//�㹦��������ʽ����ʱ�ĸ���ֵ
extern float Ref_Tan;//�㹦�����ط�ʽ�µ����и���ֵ 

//Ref.c
extern unsigned char Ref_Up_Flag;//������
extern unsigned char Ref_Down_Flag;//������
extern unsigned char Ref_Up_Old_Flag;	//��һ�����������Ű�ť״̬�������������ȴ�
extern unsigned char Ref_Down_Old_Flag;	//��һ�������ڼ��Ű�ť״̬�������������ȴ�
extern float Q_Ref;//�޹�����ֵ
//extern float Q_Ref_Set;//�޹��������õ��޹����ʸ���ֵ
extern unsigned char Q_Ref_Flag;//�޹����������־
extern float Q_Ref_Set;//������õ��޹�����ֵ
extern int Q_Ref_Count;//�޹����ʵ�����ʱ�������޹��������ڵĉ������ٶ�

extern unsigned char E_Brake_On_Flag;	//�����ƶ�Ͷ������
extern unsigned char E_Brake_Off_Flag;	//�����ƶ��˳�����
extern unsigned char E_Brake_Flag;		//�����ƶ�״̬��1->��Ͷ�� 0->δͶ��
extern unsigned char E_Brake_Off_Old;	//��һ���ڵ����ƶ��˳�����״̬ 
extern unsigned char E_Brake_Fault_Flag;//�����ƶ����� 

//Start.c
extern unsigned char Auto_Ex_Fail_Flag;//����ʧ�ܱ�־
extern unsigned char Start_Ex_V_Flag;//���˵�ѹ��ʽ����
extern unsigned char Start_Ex_Ifd_Flag;//���ŵ�����ʽ����
extern unsigned char Start_Ex_Vs_Flag;//ϵͳ��ѹ��ʽ����
extern unsigned char Start_Ex_Alpha_Flag;//���ƽǷ�ʽ����
extern unsigned char uchStart;//������ʽ
extern unsigned char Start_Old_V_Flag;//ԭ������ʽΪ���˵�ѹ��ʽ����
extern unsigned char Start_Old_Ifd_Flag;//ԭ������ʽΪ���ŵ�����ʽ����
extern unsigned char Start_Old_Vs_Flag;//ԭ������ʽΪϵͳ��ѹ��ʽ����
extern unsigned char Start_Old_Alpha_Flag;//ԭ������ʽΪ���ƽǷ�ʽ����
extern unsigned char Init_Ex_Quit_Flag;//�����˳���־
extern unsigned char Init_Ex_Flag;//Ͷ������־
extern unsigned char Init_Ex_State;//����״̬
extern unsigned char Init_Ex_Event_Flag;//Ͷ�����¼���־
extern unsigned char Start_Ex_Flag;//������־
extern unsigned int Start_Count;//�������Ա���ʱ�䣬��֤�е����ƶ�ʱ���Ա�����������п��ض������

extern unsigned char Start_Speed_Flag;//95%ת�ٱ�־
extern unsigned char Soft_Wait_Flag;//�������ȴ���־
extern int Stop_Delay_Count;//�����ɺ�(If<0.02)��ʱ����ͣ���ȴ�
extern int Stop_Delay;		//����е����ƶ����ܣ������ɺ󱣳������15s��PCC������
extern int Init_Ex_Time_Count;//������ʱ����Ͷ�������ó���10s��
extern float No_Load_Angle;//���ؽ�
extern float Start_Angle_Low;//ǿ����
extern float Start_Angle_High;//���ؽ�
extern float Soft_Ref;//���������Ƶĸ���ֵ
extern float Soft_Ref_Avg;//���������Ƶĸ���ƽ��ֵ


//calculate.c
extern unsigned char PID_Flag;//PID���Ʒ�ʽ
extern unsigned char PID_PSS_Flag;//PID+PSS���Ʒ�ʽ


//switch
extern unsigned char Emergency_Stop_Flag;//�¹�ͣ����־
extern unsigned char Emergency_Old_Flag;//ԭ���¹�ͣ����־
extern unsigned char S101_Flag;//�������ſ��ر�־
extern unsigned char S101_Old_Flag;//ԭ�з������ſ��ر�־, ������Ӧ����ſ������������
extern unsigned char S101_Rec_Flag;//���ڹ���¼���ķ������ſ���ԭ��״̬��־
extern unsigned char Local_Control_Flag;//�ֵؿ��Ʊ�־
extern unsigned char Pulse_Check_Flag;//������������־
extern short int Pulse_Check_Delay;//��������ʱ������
extern short int Pulse_Delay;//����ͨ������ر���ʱ������


//serial
extern unsigned char Wave_12_Flag;//����¼����־
extern unsigned char Wave_12_Deal_Flag;//����¼��������־
extern unsigned char Wave_60_Flag;//����¼����־
extern unsigned char Wave_60_Deal_Flag;//����¼��������־
extern unsigned char Wave_Tx_Flag;//���Ͳ������ݱ�־
extern unsigned short int Wave_Index;//��ǰ������� 
extern int* Pt_Data;//¼�����ݼ�¼ָ��
extern short int Data_Count;//����֡��ţ���3000������Ը��Ǿ�����
extern short int Pt_Data_Start;//¼���������ָ��
extern short int Pt_Data_End;//¼�������յ�ָ��
extern short int Collect_Data_Count;	//�����ռ�����������
extern short int Data_Tx_Num;	//��Ҫ���͵�¼������
extern short int Pt_Data_Tx;	//ָ����һ�����͵�¼�����ݵ�ָ��
extern unsigned char Tx_Count;//����ȡ�������ݵ����

extern unsigned char DO_Test_Flag;	//���������־
extern unsigned char DO_Test[8];	//�������Ե�����
extern unsigned char Pulse_Test;	//�������
extern unsigned char Scr_Command_Flag;//���ʹ������־ 
extern unsigned char Scr_Sensor_ID_Flag;//���ʹ��´�������ID��־ 
extern unsigned char Dis_Command_Flag;//��Ź������־  
extern unsigned char Dis_KB_Flag;//��Ź��´�k, b��־ 
extern unsigned char Dis_ID_Flag;//��Ź��´�������ID��־ 
extern unsigned char Upload_Dis_KB_Flag;//��Ź��ϴ�k, b��־ 
extern unsigned char Upload_Dis_ID_Flag;//��Ź��ϴ�������ID��־ 

// IO 
extern unsigned char Write_ROM_Flag;	//�����̻���־
extern unsigned char Upload_Data_Flag;//�ϴ���������־ 
extern short int Upload_Data_C;//�ϴ�����������֡��� 

//*****************************************************************************************************************

extern int Syn_F_Delay;	//����ͬ���źź󵽿�ʼƵ�ʼ�����ӳٴ���

extern unsigned char Low_F_Flag;		//��Ƶ��־
extern int Low_F_Count;				//��Ƶ������
extern unsigned int Syn_A_Int_Time;	//A���ж�ʱ��
extern unsigned int Syn_B_Int_Time;	//B���ж�ʱ��
extern unsigned int Syn_C_Int_Time;	//C���ж�ʱ��
extern unsigned int Syn_AB_Time;	//AB���ж�ʱ�̲�
extern unsigned int Syn_BC_Time;	//BC���ж�ʱ�̲�
extern unsigned int Syn_CA_Time;	//CA���ж�ʱ�̲�
extern unsigned int Syn_Int_Time;		//��ǰ�������ж�ʱ��

extern unsigned long Syn_A_Rising_Time;	//A��ͬ���ź�������ʱ��
extern unsigned long Syn_A_Falling_Time;	//A��ͬ���ź��½���ʱ��
extern unsigned long Syn_B_Rising_Time;	//B��ͬ���ź�������ʱ��
extern unsigned long Syn_B_Falling_Time;	//B��ͬ���ź��½���ʱ��
extern unsigned long Syn_C_Rising_Time;	//C��ͬ���ź�������ʱ��
extern unsigned long Syn_C_Falling_Time;	//C��ͬ���ź��½���ʱ��

extern unsigned char Syn_A_Work_Flag;	//A��ͬ��������־
extern unsigned char Syn_B_Work_Flag;	//B��ͬ��������־
extern unsigned char Syn_C_Work_Flag;	//C��ͬ��������־
extern unsigned char Syn_A_Err_Flag;	//A��ͬ��ԭ�����߱�־
extern unsigned char Syn_B_Err_Flag;	//B��ͬ��ԭ�����߱�־
extern unsigned char Syn_C_Err_Flag;	//C��ͬ��ԭ�����߱�־
extern unsigned char Sec_A_Flag;	//A��ͬ���������߱�־
extern unsigned char Sec_B_Flag;	//B��ͬ���������߱�־
extern unsigned char Sec_C_Flag;	//C��ͬ���������߱�־
extern unsigned char Syn_Error_Flag;		//ͬ�����ϱ�־
extern unsigned char Scr_Syn_Err_Flag[4];//ͨ�Ŵ��ݵĹ��ʹ�ͬ������
extern unsigned char Scr_Syn_Flag;//ͨ�Ŵ��ݵĹ��ʹ�ͬ�������ܱ�־

extern int Sec_A_Count;	//A��ͬ�������ӳټ�����
extern int Sec_B_Count;	//B��ͬ�������ӳټ�����
extern int Sec_C_Count;	//C��ͬ�������ӳټ�����
extern int Syn_A_Count;	//A��ͬ��ԭ���ָ��ӳټ�����
extern int Syn_B_Count;	//B��ͬ��ԭ���ָ��ӳټ�����
extern int Syn_C_Count;	//C��ͬ��ԭ���ָ��ӳټ�����

extern float PQ_Beta_Cos;			//PQУ��������ֵ
extern float PQ_Beta_Sin;			//PQУ��������ֵ
extern float Omiga_k;				//Omiga�˲�ϵ��

extern unsigned char PT_Inv_Phase_Flag;	//PT�����־
extern unsigned char CT_Inv_Phase_Flag;	//CT�����־
extern unsigned char Syn_Inv_Phase_Flag;//ͬ�������־

//RS232
extern unsigned char AsynTxData[55];	//���ڷ��ͻ���
extern unsigned char AsynTxBuf[55];		//���ڴ�������
extern unsigned char AsynRxData[16];	//���ڽ��ջ���
extern unsigned char Asyn_Buf_Flag;		//���ڴ����������ݵı�־
extern int RxNum;						//���ڽ��յ����ֽ���

//pulse.c

//GPIO.c
extern unsigned char DI_Data[8];
extern unsigned char DO_Data[8];
extern unsigned char DO_PT;				//���б���PT�����źŵ�DO1
extern unsigned int Running_Count;		//����ָʾ����˸��������10ms����1��
extern unsigned char Running_Flag;		//����ָʾ��״̬��1������2����
extern unsigned char Channel_Switch_Flag;//ͨ���л���־
extern unsigned char Channel_Switch_Command;//ͨ���л�����

//ad.c
extern float Ia_Const;	//a�ඨ�ӵ���У��ϵ��
extern float Ib_Const;
extern float Ic_Const;
extern float Va_Const;	//a����˵�ѹУ��ϵ��
extern float Vb_Const;
extern float Vc_Const;
extern float Ifd_Const;	//���ŵ���У��ϵ��
extern float Vfd_Const;	//���ŵ�ѹУ��ϵ��
extern float Vs_Const;	//ϵͳ��ѹУ��ϵ��

extern unsigned char Power_5V_Flag;	//5V��Դ�����ϱ�־
extern unsigned char Power_12V_Flag;	//12V��Դ���ϱ�־
extern unsigned char Power_n12V_Flag;	//-12V��Դ���ϱ�־

extern unsigned char AC_Power_Flag;	//AC���� 
extern unsigned char DC_Power_Flag; //DC���� 
extern unsigned char Power_24V_Flag;//24V��Դ��ʧ 
extern unsigned char Operating_Power_Flag;//������Դ��ʧ 

extern unsigned char ROM_Flag;	//
extern unsigned char Ref_Limit_Flag;	//����ֵ�ﵽ������Χ�߽�
extern unsigned char AD_Err_Flag;		//ADоƬ����
extern unsigned char Bak_AD_Err_Flag;	//��һͨ��ADоƬ����
extern unsigned char De_Ex_Fail_Flag;	//���ʧ�ܣ�δ����10s�ڽ����ŵ������͵�0.02Ifn����

// 清理重复变量声明：以下变量已在第216-232行声明，此处移除重复声明
// extern float Vai;	//a���ѹ�鲿 - 重复声明，已移除
// extern float Var;	//a���ѹʵ�� - 重复声明，已移除
// extern float Vbi;	//b���ѹ�鲿 - 重复声明，已移除
// extern float Vbr;	//b���ѹʵ�� - 重复声明，已移除
// extern float Vci;	//c���ѹ�鲿 - 重复声明，已移除
// extern float Vcr;	//c���ѹʵ�� - 重复声明，已移除
// extern float Iai;	//a������鲿 - 重复声明，已移除
// extern float Iar;	//a�����ʵ�� - 重复声明，已移除
// extern float Ibi;	//b������鲿 - 重复声明，已移除
// extern float Ibr;	//b�����ʵ�� - 重复声明，已移除
// extern float Ici;	//c������鲿 - 重复声明，已移除
// extern float Icr;	//c�����ʵ�� - 重复声明，已移除

//��ʱ���飺���������β���������˲���
extern float Vfd_Data[3];	//���ŵ�ѹ	
extern float Ifd_Data[3];	//���ŵ���
extern float Vs_Data[3];	//ϵͳ��ѹ

extern float Power_5V;		//5Vֱ����Դ�Ĳ���ֵ
extern float Power_12V;	//12Vֱ����Դ�Ĳ���ֵ
extern float Power_n12V;	//-12Vֱ����Դ�Ĳ���ֵ

extern float Alpha_Rx;
extern unsigned char Comm_Err_Flag;	//������ͨ��ͨ�Ź���
extern unsigned char Bak_Comm_Err_Flag;//��һ����ͨ����������ͨ�Ź���
extern unsigned char Anode_Syn_Flag;	//����ͬ����־
extern unsigned char Set_Pulse_Flag;	//������Ա�־
extern unsigned char Set_Pulse;		//�����Ե�����
extern unsigned char Upload_Pulse_Flag;	//Ҫ��������ϴ���ǰ���Ե�

//sci.c
extern int Asyn_Tx_Count;	//���ڷ���TxFIFO�ĵ���������
extern unsigned char Asyn_Tx_Flag;	//���ڷ��ͱ�־


//�����ƶ�
extern float E_Brake_Ifd;	// �����ƶ�Ͷ��ʱ��ת�ӵ�������ֵ
extern unsigned char SC_Flag;	//��·����״̬
extern unsigned char S102_Flag;	//����״̬
extern unsigned char S104_Flag;	//����״̬
extern unsigned char S105_Flag;	//����״̬
extern unsigned char S107_Flag;	//����״̬ 

extern unsigned char S101_On_Flag;//��S101
extern unsigned char S101_Off_Flag;//��S101
extern unsigned char S102_On_Flag;//��S102
extern unsigned char S102_Off_Flag;//��S102
extern unsigned char S104_On_Flag;//��S104
extern unsigned char S104_Off_Flag;//��S104
extern int S101_Delay;//���ز�����ʱ
extern int S102_Delay;//���ز�����ʱ
extern int S104_Delay;//���ز�����ʱ

extern unsigned char Fuse_Flag;	//����
extern unsigned char MCB_Flag;	//��������״̬
extern unsigned char Ex_T_Flag;	//���ű�澯
extern unsigned char Speed_90_Flag;//90%ת�ٱ�־���ⲿ����
extern int E_Brake_Count;		//�����ƶ�����ʱ�������
extern int E_Brake_Off_Delay;	//�����ƶ��˳����̼�ʱ��

extern unsigned char Ex_On_Flag;	//������Ͷ���־
extern unsigned char Ex_Off_Flag;	//�������˳���־
extern unsigned char Ex_Ready_Flag;//���ž�����־���ⲿ���ؾ�����
extern unsigned char Ex_On_Ready_Flag;//����Ͷ�������־
extern unsigned char Ex_Trip_Flag;	//������բ��־
extern short int Flash_Delay;		//����ָʾ����˸����
extern unsigned char AD_Vector_Flag;//�ϴ�����ʸ����־

extern unsigned char Wave_Tx;		//�����������

extern unsigned char LimitWave[8];			//ǰ4���ֽ��ǹ������ɣ���4���ֽ����Զ�¼����������

extern float DA1;					//DAͨ��1���ֵ
extern float DA2;					//DAͨ��2���ֵ

extern unsigned short int Pulse_Pointer;//�������
extern unsigned short int Phase_Offset;//ͬ���ź�������������ƫ���
extern unsigned int Rising_Edge_Flag;	//���������ر�־

extern unsigned char chSCR[7][75];		//���ʹ���Ź�����
extern unsigned char chNet[10];			//����ͨ��״̬(��������8�����ӣ��ͻ���1��������1��)
extern unsigned char Bak_Online_Flag;	//��һ����ͨ������״̬

// extern unsigned char Net_Tx_Flag;		//����ͨ��ͨ�����翪ʼ�������ݵı�־
// extern unsigned short int Net_Tx_Delay[10];	//����ͨ����ʼ���ͺ����Ķ�ʱ��ѯ������������ʱ������緢�ͱ�־
// extern unsigned short int Net_Rx_Delay[10];//����ͨ���ϴν�����ɺ����Ķ�ʱ��ѯ����
// extern unsigned char Net_Tx_Error[10];	//����ͨ�����ͳ�ʱ
// extern unsigned char Net_Rx_Error[10];	//����ͨ�����ͳ�ʱ
extern unsigned short int Scr_Num;		//���ʹ�����������ͨ�ż���
extern unsigned char Net_Flag;//�����Ƿ������� 

extern unsigned char Net_Client[10];	//�ͻ����Ƿ�����˱���ͨ��
extern unsigned char Net_Client_Delay[10];	//�ͻ���ͨ�Ŷ�ʧ����ʱ

extern unsigned char uchNodeNum;	//����

//Over I
extern float Over_I_Hold;
extern float Over_I_k;
extern float Over_I_n;
extern float Over_I_Count;//���ӹ���ʱ����������

//Vamax=1.35Vanode/(0.9Vf0)cos(amax)
//Vamin=1.35Vanode/(0.9Vf0)cos(amax)
extern float Ref_Alpha_Max;	//���ƽ�����
extern float Ref_Alpha_Min;	//���ƽ�����
extern float Vamax;		//��������޷�
extern float Vamin;		//��������޷�
extern float Vanode_V0;	//1.35Vanode/(0.9Vf0), ������ѹ�����ŵ�ѹ��׼ֵ�ı�, Ufb=0.9Uf0, Uf0Ϊ���ض�����µ����ŵ�ѹ, ���ڼ���������������޺�����
extern float Ref_V0_Max;	//���ص�ѹ����ֵ����
extern float Ref_V0_Min;	//���ص�ѹ����ֵ����
extern float Ref_V1_Max;	//������ѹ����ֵ����
extern float Ref_V1_Min;	//������ѹ����ֵ����
extern float Ref_Ifd0_Max;	//�������ŵ�������ֵ����
extern float Ref_Ifd0_Min;	//�������ŵ�������ֵ����
extern float Ref_Ifd1_Max;	//�������ŵ�������ֵ����
extern float Ref_Ifd1_Min;	//�������ŵ�������ֵ����
extern float Ref_Q_Max;	//�޹����ʸ���ֵ����
extern float Ref_Q_Min;	//�޹����ʸ���ֵ����
extern float Ref_Cos_Max;	//������������ֵ����
extern float Ref_Cos_Min;	//������������ֵ����

extern float PID_X[2];		//����PID�������ڵ��м�״̬��, ����PID���㹲��, ��һ�μ���Ļ��ֻ���
extern float PID_X_Rx[2];	//��һ����ͨ������PID�������ڵ��м�״̬��
extern float PID_X_Q_Max[2];	//���޹�������PID���Ƶ��м�״̬��
extern float PID_X_Q_Min[2];	//Ƿ��������PID���Ƶ��м�״̬��
extern float PID_X_Over_I_Lag[2];//���ӵ�������(����)��PID���Ƶ��м�״̬��
extern float PID_X_Over_I_Lead[2];//���ӵ�������(����)��PID���Ƶ��м�״̬��
extern float PID_X_Over_Ex[2];	//ǿ��������PID���Ƶ��м�״̬��
extern float PID_X_AVR[2];	//AVR��ʽPID���Ƶ��м�״̬��
extern float PID_X_FCR[2];	//FCR��ʽPID���Ƶ��м�״̬��
extern float PID_X_Q[2];	//Q��ʽPID���Ƶ��м�״̬��
extern float PID_X_PSS[2];	//PSS�������PID���Ƶ��м�״̬��
extern float PID_X_Ifd_Min[2];//��С���ŵ������Ƶ��м�״̬��

extern float PID_T_Q_Max[4];	//���޹�������PID���Ƶ�4��ʱ�䳣��
extern float PID_T_Q_Min[4];	//Ƿ��������PID���Ƶ�4��ʱ�䳣��
extern float PID_T_Over_I_Lag[4];//���ӵ�������(����)��PID���Ƶ�4��ʱ�䳣��
extern float PID_T_Over_I_Lead[4];//���ӵ�������(����)��PID���Ƶ�4��ʱ�䳣��
extern float PID_T_Over_Ex[4];	//ǿ��������PID���Ƶ�4��ʱ�䳣��
extern float PID_T_AVR[4];	//AVR��ʽʱPID���Ƶ�4��ʱ�䳣��
extern float PID_T_FCR[4];	//FCR��ʽʱPID���Ƶ�4��ʱ�䳣��
extern float PID_T_Q[4];	//Q��ʽʱPID���Ƶ�4��ʱ�䳣��

extern float PID_K_Q_Max;//���޹�ʱPID��������
extern float PID_K_Q_Min;//Ƿ��ʱPID��������
extern float PID_K_Over_I_Lag;//���ӵ�������(����)ʱPID��������
extern float PID_K_Over_I_Lead;//���ӵ�������(����)ʱPID��������
extern float PID_K_Over_Ex;//ǿ������ʱPID��������
extern float PID_K_AVR;	//AVR��ʽʱPID��������
extern float PID_K_FCR;	//FCR��ʽʱPID��������
extern float PID_K_Q;		//Q��ʽʱPID��������

extern float Ref_Qmin_t;
extern float Ref_Qmax_t;
extern float Puel;

extern unsigned char Scr_ROM_Flag[5];	//���ʹ�����̻������־��֪ͨ��������ݸ�����
extern unsigned char Scr_ROM_Result[5];//���ʹ�����̻���� 
extern unsigned short int Scr_Ifd_E[5][2];//�������ƵĿɿع� 
extern unsigned char SCR_Error_Flag[5]; //���ʹ���ϱ�־ 

extern unsigned char Discharge_ROM_Result;//��Ź�����̻���� 

extern float Kcr;//����ʼ��������
extern float Kef;//���Ż���������
extern float Klim_hir;//Ӳ���������޷�����
extern float Vlim_hir;//Ӳ�����޷� 
extern float Koel;//���ŵ������Ƶ�ƫ������
extern float Kt;//��ʱ����
extern float Kii;//���Զ��ӹ������Ƶ�ƫ������ 
extern float Kic;//���Զ��ӹ������Ƶ�ƫ������ 
extern float Kuel;//Ƿ������ƫ������ 

extern unsigned short int Cycle_Count;//��ѭ�������� 
extern unsigned short int Ifd_E_Count;//����������ʱ������ 
extern unsigned char Ifd_E_N_Flag;//�������ƶ��������������ڱ�־ 
extern unsigned short int Ifd_E_Delay;//����������ʱ 
extern unsigned char Ifd_E_Flag;//���ܾ�����־, 0: ��Ȼ���� 1: �ܾ��� 2: ����� 
extern float Thyristor_I[6][6];//�����ʹ�բ�ܵ��� 

//PSS4B
extern float PSS4B_T[3][12];//T1~T12
extern float PSS4B_K[3][5];//K1, K11, K2, K22, K
extern float PSS4B_Omiga_T[3];//Tw1, Tw2, T9
extern float PSS4B_P_T[3];//Tw3, Tw4, T7
extern float Upss_M[4];
extern float PSS4B_P_K;//P_K2
extern float PSS4B_U;
extern unsigned char PSS4B_Flag; 

extern short int Comm_Index;//������Ϊ2֡����, �������רΪ����ͨ��֡���� 

extern unsigned char Version[4];//�汾��Ϣ 

extern int Start_Ref_Delay;//����������ֵ���ֲ���������ʱ

extern unsigned char Init_Ex_V_Flag;	//����Ͷ��ǰ������ȫ����־
extern int Init_Ex_V_Delay;			//����Ͷ��ǰ������ȫ��ʱ�� 

extern unsigned char Write_ROM_Step;//��ͣIEC61850ͨ�ţ��Ա㽫����д��Flash

extern unsigned char Scr_E_Syn_T_Flag;//�ϴ�����ʱ�л���������

extern unsigned char Command[8];//�������Ľ������� 

extern unsigned char ucMonitor[100];//��� 

extern float Uv;//AVR ������ 
extern float Uqi;//���޹�������
extern float Uqc;//Ƿ�����ƿ�����
extern float Uii;//���Զ��ӹ���������
extern float Uic;//���Զ��ӹ���������
extern float Uifd;//ǿ�����ƿ�����
extern float Upss;//PSS������
extern float Uifd_min;//��С���ŵ������ƿ����� 

extern unsigned char Net_Monitor_Flag;//������һҳ������ݼ�¼ 
extern int Net_Monitor_C;
extern int Net_Monitor_Index;
extern unsigned char ucNet_M[2][4096];
extern int ms_C;
extern unsigned char Monitor_Upload_Flag;//�ϴ�������ݱ�־ 

extern unsigned char Net_Write_ROM_Flag;//�������������̻����ܱ�־ 
extern unsigned char Net_Write_ROM_Old_Flag;//�������������̻����ܵľ��б�־ 

extern unsigned char Start_E_Brake_Flag;//�����ƶ�Ͷ��ʱ�������׶α�־, �˽׶ν����ŵ����������ڵ�����ֵ 
extern int Start_E_Brake_C;//�����ƶ�Ͷ��׶μ����� 

extern int Sensor_ID_Num;//�ѽ��յĴ�����ID֡�� 

extern unsigned char Fan_On_Flag;//�������߹���Ͷ�� 
extern unsigned char Fan_Flag;//�������߹���״̬ 
extern float Tac[10];//�������߹���ص�10���¶� 
extern float T_Fan;//��������¶� 
extern float I_Fan;//����������� 

extern float eBrakeStart;//�����ƶ�����ֵ����ʱ��(s)
extern float eBrakeT;//�����ƶ������������ʱ��(s)

extern unsigned char t_Op[8];//�������紫�ݵĶ�ʱ���� 

extern float Bak_Va;
extern float Bak_Vb;
extern float Bak_Vc;
extern int Bak_PT_A_Count;
extern int Bak_PT_B_Count;
extern int Bak_PT_C_Count;
extern unsigned char Bak_PT_A_Flag;
extern unsigned char Bak_PT_B_Flag;
extern unsigned char Bak_PT_C_Flag;

extern unsigned char Start_PT_Flag;
extern int Start_PT_Delay;
extern unsigned int Scr_T;
extern unsigned char SCR_fan_state[4];
//===========================================================================
// End of file.
//===========================================================================
