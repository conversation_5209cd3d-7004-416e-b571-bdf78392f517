/********************************************************************
 * COPYRIGHT --  
 ********************************************************************
 * Program: IAEC-4000
 * File: command.h
 * Author: Yu Xiang
 * Created: April 24, 2018
 *******************************************************************/
 #ifndef __COMMAND_H
#define __COMMAND_H

 short int CRC16(unsigned char *Data, int length);
 
 void Byte_To_Float(float *data,unsigned char * pB);
 
 void Float_To_Byte(float data,unsigned char *pB);
 
 void RS232_Comm(void);
 
 void RS485_Comm(void);
 
 void CAN_Comm(void);
 
 void Upload_Ex_Data(void);
 
 void Init_DO_Test(void);
 
 void Upload_AD_Const(void);
 
 void Switch_Operating_Mode(unsigned char nMode);
 
 void Switch_Start_Mode(unsigned char nMode);
 
 void Step_Check(char chIndex, unsigned char Extra_Flag);
 
 void Cancel_Test(void);
 
 void Upload_Ref(int ROM_Result);
 
 #endif
 //===========================================================================
// End of file.
//===========================================================================
