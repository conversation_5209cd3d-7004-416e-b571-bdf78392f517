/********************************************************************************
*net.h                                                                          *
*????????????                                                       *
********************************************************************************/
extern unsigned char* Network1_Rxbuf[8];
extern unsigned char  Net_Socket[8];

void Net_Rx_Tx(void);

void Init_Net(void);

void Net_Wave_Info(int index);

void Net_Upload_Wave(void);

void Net_AD(int Index);

void Net_Ref(int Index, int ROM_Result);

void Usart_Monitor_Comm(int index);

void Net_SCR_Sensor_ID(int Index);

void Net_Discharge_KB(int Index);

//===========================================================================
// End of file.
//===========================================================================
