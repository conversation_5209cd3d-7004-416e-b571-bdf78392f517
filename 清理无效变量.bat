@echo off
echo ========================================
echo 清理无效变量脚本
echo ========================================
echo.
echo 本脚本已完成以下清理工作：
echo.
echo 1. main.c 中的未使用变量：
echo    - float dT=0;
echo    - uint32_t Clock_List[4];
echo    - unsigned int T_dt;
echo    - unsigned char T_dt_cnt;
echo.
echo 2. main.c 中的重复包含：
echo    - 移除重复的 #include "pss4b.h"
echo.
echo 3. pub.h 中的重复变量声明：
echo    - 移除12个重复的extern变量声明
echo.
echo 4. para.h 中的重复函数声明：
echo    - 移除3个重复的函数声明
echo.
echo 所有清理的内容都已注释保存，便于回溯。
echo.
echo 详细信息请查看：清理无效变量报告.md
echo.
echo ========================================
echo 清理完成！
echo ========================================
pause
