/********************************************************************
*main.c																*
*������������������Ӳ����ʼ������ѭ����								*
********************************************************************/ 
#include <gd32f4xx.h>
#include "net.h"
// 清理重复包含：移除重复的 pss4b.h
// #include "pss4b.h"  // 重复包含，已移除（第17行保留）
#include "WatchDog.h"
#include "DIDO.h"
#include "para.h"
#include "command.h"
#include "syn.h"
#include "PID.h"
#include "AI.h"
#include "pss.h"
#include "start.h"
#include "pss4b.h"
#include "limit.h"
#include "ref.h"
#include "spi.h"

#include "pub.h"

extern void Net_Write_ROM(void);   


static void Boot_Delay(void);


// 清理无效变量：移除未使用的变量声明
// float dT=0;  // 未使用，已移除
// uint32_t Clock_List[4];  // 未使用，已移除
// unsigned int T_dt;  // 未使用，已移除
// unsigned char T_dt_cnt;  // 未使用，已移除
extern unsigned char test_cnt[4];
/************************************************************************
*main																	*
*�������������ϵ��ʼ����������ѭ����									*
************************************************************************/
int main(void)
{
	unsigned int T_Now, T_Old;
	Boot_Delay();
	nvic_priority_group_set(NVIC_PRIGROUP_PRE2_SUB2);//�������ȼ���ֵ
	Init_Once();	//������ʼ��	
	Init_Hardware();//Ӳ����ʼ��
	 
	T_Old=TIMER_CNT(TIMER4);  //
	T_Now=T_Old;	


	for(;;)
	{
			while(T_Now-T_Old<1000000) //��֤ѭ��ʱ���Լ10ms
		{
			T_Now=TIMER_CNT(TIMER4);
		}
	
		T_Old=T_Now;//ѭ��ʱ�����
		Cal_Avg();	//V��I��P��Q��F��Period��Ifd��Vfd����
		PSS2A();	//PSS���������� 
		PSS4B();
		Get_Alpha();//PID���Ƽ���
		Stop_Machine_Check();//���
		Start_Check();//����
		Limit_Check();//���Ʊ���
		Upload_Ex_Data();//�ϴ�������
		Net_Write_ROM();
		Collect_Data();//¼��, ��һ����ѭ�����ڼ�¼һ�����ݵ�
		DIDO();			//������ˢ��	
		Ref_Com();
		SPI_work_ctrl();
		
		test_cnt[0]++;
	if(test_cnt[0]>100)
	{
	test_cnt[1]++;
		test_cnt[0]=0;
	}
	if(test_cnt[1]>100)
	{
	test_cnt[2]++;
		test_cnt[1]=0;
	}
	if(test_cnt[2]>100)
	{
	test_cnt[3]++;
		test_cnt[2]=0;
	}
	
	}//��ѭ�� 
}
void Boot_Delay(void)
{
	volatile int i = 50, j = 100000;
	//��ʱһ��ʱ������//?us//1000��
	while(i--)
	{
		//ѭ��100000�δ�Լ
		j = 100000;
		while(j--);
	}
}



//===========================================================================
// End of file.
//===========================================================================

