Dependencies for Project 'Ex_gd32', Target 'ARM_Ex': (DO NOT MODIFY !)
CompilerVersion: 5060960::V5.06 update 7 (build 960)::.\ARMCC
F (.\stdDriver\src\gd32f4xx_adc.c)(0x635F380C)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_adc.o --omf_browse .\objects\gd32f4xx_adc.crf --depend .\objects\gd32f4xx_adc.d)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_can.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_can.o --omf_browse .\objects\gd32f4xx_can.crf --depend .\objects\gd32f4xx_can.d)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_crc.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_crc.o --omf_browse .\objects\gd32f4xx_crc.crf --depend .\objects\gd32f4xx_crc.d)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_ctc.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_ctc.o --omf_browse .\objects\gd32f4xx_ctc.crf --depend .\objects\gd32f4xx_ctc.d)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_dac.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_dac.o --omf_browse .\objects\gd32f4xx_dac.crf --depend .\objects\gd32f4xx_dac.d)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_dbg.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_dbg.o --omf_browse .\objects\gd32f4xx_dbg.crf --depend .\objects\gd32f4xx_dbg.d)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_dci.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_dci.o --omf_browse .\objects\gd32f4xx_dci.crf --depend .\objects\gd32f4xx_dci.d)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_dma.c)(0x6360E38A)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_dma.o --omf_browse .\objects\gd32f4xx_dma.crf --depend .\objects\gd32f4xx_dma.d)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_enet.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_enet.o --omf_browse .\objects\gd32f4xx_enet.crf --depend .\objects\gd32f4xx_enet.d)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
F (.\stdDriver\src\gd32f4xx_exmc.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_exmc.o --omf_browse .\objects\gd32f4xx_exmc.crf --depend .\objects\gd32f4xx_exmc.d)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_exti.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_exti.o --omf_browse .\objects\gd32f4xx_exti.crf --depend .\objects\gd32f4xx_exti.d)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_fmc.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_fmc.o --omf_browse .\objects\gd32f4xx_fmc.crf --depend .\objects\gd32f4xx_fmc.d)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_fwdgt.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_fwdgt.o --omf_browse .\objects\gd32f4xx_fwdgt.crf --depend .\objects\gd32f4xx_fwdgt.d)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_gpio.c)(0x6362705E)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_gpio.o --omf_browse .\objects\gd32f4xx_gpio.crf --depend .\objects\gd32f4xx_gpio.d)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_i2c.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_i2c.o --omf_browse .\objects\gd32f4xx_i2c.crf --depend .\objects\gd32f4xx_i2c.d)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_ipa.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_ipa.o --omf_browse .\objects\gd32f4xx_ipa.crf --depend .\objects\gd32f4xx_ipa.d)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_iref.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_iref.o --omf_browse .\objects\gd32f4xx_iref.crf --depend .\objects\gd32f4xx_iref.d)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_misc.c)(0x6360B65A)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_misc.o --omf_browse .\objects\gd32f4xx_misc.crf --depend .\objects\gd32f4xx_misc.d)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_pmu.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_pmu.o --omf_browse .\objects\gd32f4xx_pmu.crf --depend .\objects\gd32f4xx_pmu.d)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_rcu.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_rcu.o --omf_browse .\objects\gd32f4xx_rcu.crf --depend .\objects\gd32f4xx_rcu.d)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_rtc.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_rtc.o --omf_browse .\objects\gd32f4xx_rtc.crf --depend .\objects\gd32f4xx_rtc.d)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_sdio.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_sdio.o --omf_browse .\objects\gd32f4xx_sdio.crf --depend .\objects\gd32f4xx_sdio.d)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_spi.c)(0x6363ABBA)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_spi.o --omf_browse .\objects\gd32f4xx_spi.crf --depend .\objects\gd32f4xx_spi.d)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_syscfg.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_syscfg.o --omf_browse .\objects\gd32f4xx_syscfg.crf --depend .\objects\gd32f4xx_syscfg.d)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_timer.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_timer.o --omf_browse .\objects\gd32f4xx_timer.crf --depend .\objects\gd32f4xx_timer.d)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_tli.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_tli.o --omf_browse .\objects\gd32f4xx_tli.crf --depend .\objects\gd32f4xx_tli.d)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_trng.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_trng.o --omf_browse .\objects\gd32f4xx_trng.crf --depend .\objects\gd32f4xx_trng.d)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_usart.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_usart.o --omf_browse .\objects\gd32f4xx_usart.crf --depend .\objects\gd32f4xx_usart.d)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\gd32f4xx_wwdgt.c)(0x62D7C0AE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gd32f4xx_wwdgt.o --omf_browse .\objects\gd32f4xx_wwdgt.crf --depend .\objects\gd32f4xx_wwdgt.d)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\system_gd32f4xx.c)(0x618890E8)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\system_gd32f4xx.o --omf_browse .\objects\system_gd32f4xx.crf --depend .\objects\system_gd32f4xx.d)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\stdDriver\src\startup_gd32f450.s)(0x603757B0)(--cpu Cortex-M4.fp.sp -g --apcs=interwork 

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

--pd "__UVISION_VERSION SETA 536" --pd "GD32F450 SETA 1"

--list .\listings\startup_gd32f450.lst --xref -o .\objects\startup_gd32f450.o --depend .\objects\startup_gd32f450.d)
F (.\source\GPIO.C)(0x644A1A1A)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\gpio.o --omf_browse .\objects\gpio.crf --depend .\objects\gpio.d)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
I (source\pub.h)(0x68371526)
F (.\source\nandflash_sram.c)(0x65B79605)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\nandflash_sram.o --omf_browse .\objects\nandflash_sram.crf --depend .\objects\nandflash_sram.d)
I (source\main.h)(0x64238FAD)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
I (source\AD57x4.h)(0x639C2993)
I (source\nandflash_sram.h)(0x64140F37)
I (source\TIM.h)(0x51FB7CAC)
I (source\GPIO.h)(0x527C5DA2)
I (source\pub.h)(0x68371526)
I (source\WatchDog.h)(0x52087C12)
I (source\DIDO.h)(0x5D48DE20)
F (.\source\optical_fiber.c)(0x65B9DA8B)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\optical_fiber.o --omf_browse .\objects\optical_fiber.crf --depend .\objects\optical_fiber.d)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
I (source\pub.h)(0x68371526)
F (.\source\WatchDog.c)(0x64141070)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\watchdog.o --omf_browse .\objects\watchdog.crf --depend .\objects\watchdog.d)
I (source\main.h)(0x64238FAD)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
I (source\AD57x4.h)(0x639C2993)
I (source\nandflash_sram.h)(0x64140F37)
I (source\TIM.h)(0x51FB7CAC)
I (source\GPIO.h)(0x527C5DA2)
I (source\pub.h)(0x68371526)
I (source\WatchDog.h)(0x52087C12)
I (source\DIDO.h)(0x5D48DE20)
F (.\source\AD57x4.c)(0x65E96AE3)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\ad57x4.o --omf_browse .\objects\ad57x4.crf --depend .\objects\ad57x4.d)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\source\internet.c)(0x6836C520)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\internet.o --omf_browse .\objects\internet.crf --depend .\objects\internet.d)
I (source\internet.h)(0x641BABF8)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (source\para.h)(0x64164B4C)
I (source\w5200Reg.h)(0x645D9F00)
F (.\source\CL1606_SPI.c)(0x649E3186)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\cl1606_spi.o --omf_browse .\objects\cl1606_spi.crf --depend .\objects\cl1606_spi.d)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (source\CL1606_SPI.h)(0x64166227)
F (.\source\flash.c)(0x65AB910E)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\flash.o --omf_browse .\objects\flash.crf --depend .\objects\flash.d)
I (source\flash.h)(0x65AB8FEF)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
F (.\source\AI.c)(0x67C172E1)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\ai.o --omf_browse .\objects\ai.crf --depend .\objects\ai.d)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
I (source\pub.h)(0x68371526)
I (source\const.h)(0x6836C50B)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (source\CL1606_SPI.h)(0x64166227)
I (source\internet.h)(0x641BABF8)
I (source\net.h)(0x64142F44)
I (source\command.h)(0x5AFA3F72)
I (source\limit.h)(0x5AF94904)
I (source\AD57x4.h)(0x639C2993)
I (source\para.h)(0x64164B4C)
F (.\source\DIDO.c)(0x66C44140)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\dido.o --omf_browse .\objects\dido.crf --depend .\objects\dido.d)
I (source\pub.h)(0x68371526)
I (source\const.h)(0x6836C50B)
I (source\mode.h)(0x5AF908B2)
I (source\GPIO.h)(0x527C5DA2)
I (source\optical.h)(0x5AFA3746)
I (source\start.h)(0x5AF902F6)
I (source\limit.h)(0x5AF94904)
F (.\source\limit.c)(0x6836C5D7)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\limit.o --omf_browse .\objects\limit.crf --depend .\objects\limit.d)
I (source\pub.h)(0x68371526)
I (source\const.h)(0x6836C50B)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (source\para.h)(0x64164B4C)
I (source\syn.h)(0x5AF904D2)
I (source\optical.h)(0x5AFA3746)
I (source\mode.h)(0x5AF908B2)
F (.\source\main.c)(0x687EFF5C)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\main.o --omf_browse .\objects\main.crf --depend .\objects\main.d)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
I (source\net.h)(0x64142F44)
I (source\pss4b.h)(0x5AF902F6)
I (source\WatchDog.h)(0x52087C12)
I (source\DIDO.h)(0x5D48DE20)
I (source\para.h)(0x64164B4C)
I (source\command.h)(0x5AFA3F72)
I (source\syn.h)(0x5AF904D2)
I (source\PID.h)(0x5AFA51CC)
I (source\AI.h)(0x5F2390F0)
I (source\pss.h)(0x5AF902F6)
I (source\start.h)(0x5AF902F6)
I (source\limit.h)(0x5AF94904)
I (source\ref.h)(0x5AF902F6)
I (source\spi.h)(0x65D3194A)
I (source\pub.h)(0x68371526)
F (.\source\mode.c)(0x664D864E)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\mode.o --omf_browse .\objects\mode.crf --depend .\objects\mode.d)
I (source\pub.h)(0x68371526)
I (source\const.h)(0x6836C50B)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
F (.\source\net.c)(0x6840466F)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\net.o --omf_browse .\objects\net.crf --depend .\objects\net.d)
I (source\internet.h)(0x641BABF8)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (source\pub.h)(0x68371526)
I (source\nandflash_sram.h)(0x64140F37)
I (source\const.h)(0x6836C50B)
I (source\AD57x4.h)(0x639C2993)
I (source\optical.h)(0x5AFA3746)
I (source\para.h)(0x64164B4C)
I (source\pss.h)(0x5AF902F6)
I (source\command.h)(0x5AFA3F72)
I (source\start.h)(0x5AF902F6)
I (source\syn.h)(0x5AF904D2)
I (source\DIDO.h)(0x5D48DE20)
F (.\source\optical.c)(0x687EFD03)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\optical.o --omf_browse .\objects\optical.crf --depend .\objects\optical.d)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
I (source\Optical_fiber.h)(0x648C0BEC)
I (source\pub.h)(0x68371526)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (source\command.h)(0x5AFA3F72)
I (source\AI.h)(0x5F2390F0)
I (source\PID.h)(0x5AFA51CC)
I (source\net.h)(0x64142F44)
I (source\para.h)(0x64164B4C)
F (.\source\para.c)(0x683713DE)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\para.o --omf_browse .\objects\para.crf --depend .\objects\para.d)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
I (source\nandflash_sram.h)(0x64140F37)
I (source\internet.h)(0x641BABF8)
I (source\pub.h)(0x68371526)
I (source\const.h)(0x6836C50B)
I (source\TIM.h)(0x51FB7CAC)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (source\pss.h)(0x5AF902F6)
I (source\AD57x4.h)(0x639C2993)
I (source\CL1606_SPI.h)(0x64166227)
I (source\W25QXX.h)(0x67089580)
I (source\GPIO.h)(0x527C5DA2)
I (source\DIDO.h)(0x5D48DE20)
I (source\WatchDog.h)(0x52087C12)
I (source\optical_fiber.h)(0x648C0BEC)
I (source\syn.h)(0x5AF904D2)
I (source\pss4b.h)(0x5AF902F6)
I (source\net.h)(0x64142F44)
I (source\start.h)(0x5AF902F6)
I (source\mode.h)(0x5AF908B2)
I (source\limit.h)(0x5AF94904)
I (source\command.h)(0x5AFA3F72)
I (source\spi.h)(0x65D3194A)
I (source\PID.h)(0x5AFA51CC)
I (source\flash.h)(0x65AB8FEF)
F (.\source\pub.c)(0x683714F8)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\pub.o --omf_browse .\objects\pub.crf --depend .\objects\pub.d)
F (.\source\ref.c)(0x664D8753)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\ref.o --omf_browse .\objects\ref.crf --depend .\objects\ref.d)
I (source\pub.h)(0x68371526)
I (source\const.h)(0x6836C50B)
I (source\mode.h)(0x5AF908B2)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
F (.\source\syn.c)(0x65BB0456)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\syn.o --omf_browse .\objects\syn.crf --depend .\objects\syn.d)
I (source\pub.h)(0x68371526)
I (source\const.h)(0x6836C50B)
I (source\nandflash_sram.h)(0x64140F37)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
I (source\internet.h)(0x641BABF8)
I (source\optical.h)(0x5AFA3746)
I (source\para.h)(0x64164B4C)
I (source\command.h)(0x5AFA3F72)
F (.\source\TIM.C)(0x65A8B703)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\tim.o --omf_browse .\objects\tim.crf --depend .\objects\tim.d)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
I (source\pub.h)(0x68371526)
I (source\AI.h)(0x5F2390F0)
I (source\optical.h)(0x5AFA3746)
I (source\syn.h)(0x5AF904D2)
I (source\command.h)(0x5AFA3F72)
I (source\net.h)(0x64142F44)
F (.\source\start.c)(0x5F2811AA)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\start.o --omf_browse .\objects\start.crf --depend .\objects\start.d)
I (source\pub.h)(0x68371526)
I (source\const.h)(0x6836C50B)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (source\para.h)(0x64164B4C)
I (source\mode.h)(0x5AF908B2)
I (source\limit.h)(0x5AF94904)
I (source\DIDO.h)(0x5D48DE20)
F (.\source\PID.c)(0x683714C2)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\pid.o --omf_browse .\objects\pid.crf --depend .\objects\pid.d)
I (source\pub.h)(0x68371526)
I (source\const.h)(0x6836C50B)
I (source\AD57x4.h)(0x639C2993)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (source\para.h)(0x64164B4C)
I (source\mode.h)(0x5AF908B2)
I (source\command.h)(0x5AFA3F72)
I (source\limit.h)(0x5AF94904)
I (source\optical.h)(0x5AFA3746)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
F (.\source\pss.c)(0x65BB0456)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\pss.o --omf_browse .\objects\pss.crf --depend .\objects\pss.d)
I (source\pub.h)(0x68371526)
I (source\const.h)(0x6836C50B)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (source\AD57x4.h)(0x639C2993)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
F (.\source\spi.c)(0x674FF2A8)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\spi.o --omf_browse .\objects\spi.crf --depend .\objects\spi.d)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
I (source\spi.h)(0x65D3194A)
I (source\pub.h)(0x68371526)
I (source\para.h)(0x64164B4C)
F (.\source\pss4b.c)(0x5AEBE97A)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\pss4b.o --omf_browse .\objects\pss4b.crf --depend .\objects\pss4b.d)
I (source\pub.h)(0x68371526)
I (source\const.h)(0x6836C50B)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
F (.\source\command.c)(0x6836C4E5)(-c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I .\source -I .\stdDriver\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.3.0\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F450 -DGD32F4XX -DUSE_STDPERIPH_DRIVER

-o .\objects\command.o --omf_browse .\objects\command.crf --depend .\objects\command.d)
I (source\pub.h)(0x68371526)
I (source\const.h)(0x6836C50B)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (.\stdDriver\inc\gd32f4xx.h)(0x6363A22C)
I (.\stdDriver\inc\core_cm4.h)(0x62D7C0B0)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (.\stdDriver\inc\core_cmInstr.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cmFunc.h)(0x62D7C0B0)
I (.\stdDriver\inc\core_cm4_simd.h)(0x62D7C0B0)
I (.\stdDriver\inc\system_gd32f4xx.h)(0x603757B0)
I (.\stdDriver\inc\gd32f4xx_libopt.h)(0x603757AE)
I (.\stdDriver\inc\gd32f4xx_rcu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_adc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_can.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_crc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ctc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dac.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dbg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dci.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_dma.h)(0x6360E7DE)
I (.\stdDriver\inc\gd32f4xx_exti.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_fwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_gpio.h)(0x6361E1B8)
I (.\stdDriver\inc\gd32f4xx_syscfg.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_i2c.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_iref.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_pmu.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_rtc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_sdio.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_spi.h)(0x6363B4F2)
I (.\stdDriver\inc\gd32f4xx_timer.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_trng.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_usart.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_wwdgt.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_misc.h)(0x6360B788)
I (.\stdDriver\inc\gd32f4xx_enet.h)(0x62D7C0AE)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (.\stdDriver\inc\gd32f4xx_exmc.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_ipa.h)(0x62D7C0AE)
I (.\stdDriver\inc\gd32f4xx_tli.h)(0x62D7C0AE)
I (source\mode.h)(0x5AF908B2)
I (source\nandflash_sram.h)(0x64140F37)
I (source\start.h)(0x5AF902F6)
I (source\pss.h)(0x5AF902F6)
I (source\para.h)(0x64164B4C)
I (source\optical.h)(0x5AFA3746)
I (source\syn.h)(0x5AF904D2)
I (source\AD57x4.h)(0x639C2993)
