--cpu=Cortex-M4.fp.sp
".\objects\gd32f4xx_adc.o"
".\objects\gd32f4xx_can.o"
".\objects\gd32f4xx_crc.o"
".\objects\gd32f4xx_ctc.o"
".\objects\gd32f4xx_dac.o"
".\objects\gd32f4xx_dbg.o"
".\objects\gd32f4xx_dci.o"
".\objects\gd32f4xx_dma.o"
".\objects\gd32f4xx_enet.o"
".\objects\gd32f4xx_exmc.o"
".\objects\gd32f4xx_exti.o"
".\objects\gd32f4xx_fmc.o"
".\objects\gd32f4xx_fwdgt.o"
".\objects\gd32f4xx_gpio.o"
".\objects\gd32f4xx_i2c.o"
".\objects\gd32f4xx_ipa.o"
".\objects\gd32f4xx_iref.o"
".\objects\gd32f4xx_misc.o"
".\objects\gd32f4xx_pmu.o"
".\objects\gd32f4xx_rcu.o"
".\objects\gd32f4xx_rtc.o"
".\objects\gd32f4xx_sdio.o"
".\objects\gd32f4xx_spi.o"
".\objects\gd32f4xx_syscfg.o"
".\objects\gd32f4xx_timer.o"
".\objects\gd32f4xx_tli.o"
".\objects\gd32f4xx_trng.o"
".\objects\gd32f4xx_usart.o"
".\objects\gd32f4xx_wwdgt.o"
".\objects\system_gd32f4xx.o"
".\objects\startup_gd32f450.o"
".\objects\gpio.o"
".\objects\nandflash_sram.o"
".\objects\optical_fiber.o"
".\objects\watchdog.o"
".\objects\ad57x4.o"
".\objects\internet.o"
".\objects\cl1606_spi.o"
".\objects\flash.o"
".\objects\ai.o"
".\objects\dido.o"
".\objects\limit.o"
".\objects\main.o"
".\objects\mode.o"
".\objects\net.o"
".\objects\optical.o"
".\objects\para.o"
".\objects\pub.o"
".\objects\ref.o"
".\objects\syn.o"
".\objects\tim.o"
".\objects\start.o"
".\objects\pid.o"
".\objects\pss.o"
".\objects\spi.o"
".\objects\pss4b.o"
".\objects\command.o"
--strict --scatter ".\Objects\Ex_gd32.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\Listings\Ex_gd32.map" -o .\Objects\Ex_gd32.axf