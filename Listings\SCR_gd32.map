Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_debug_freeze_disable) refers to gd32f4xx_dbg.o(i.dbg_periph_disable) for dbg_periph_disable
    gd32f4xx_can.o(i.can_debug_freeze_enable) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_receive_message_length_get) for can_receive_message_length_get
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_error_get) for can_error_get
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_enet.o(i.enet_initpara_reset) for enet_initpara_reset
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_tx_disable) for enet_tx_disable
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_rx_disable) for enet_rx_disable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_tx_enable) for enet_tx_enable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_rx_enable) for enet_rx_enable
    gd32f4xx_enet.o(i.enet_frame_receive) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_frame_transmit) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_config) for enet_phy_config
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_default_init) for enet_default_init
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_config) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_reset) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_phyloopback_disable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phyloopback_enable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_registers_get) refers to gd32f4xx_enet.o(.constdata) for enet_reg_tab
    gd32f4xx_enet.o(i.enet_rxframe_drop) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(i.enet_rxframe_drop) for enet_rxframe_drop
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxprocess_check_recovery) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_tx_disable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_enet.o(i.enet_tx_enable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_fmc.o(i.fmc_bank0_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_bank1_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_byte_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_halfword_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_mass_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_ready_wait) refers to gd32f4xx_fmc.o(i.fmc_state_get) for fmc_state_get
    gd32f4xx_fmc.o(i.fmc_sector_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_word_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_security_protection_config) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_user_write) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_i2c.o(i.i2c_clock_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_misc.o(i.nvic_irq_enable) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_pmu.o(i.pmu_highdriver_switch_select) refers to gd32f4xx_pmu.o(i.pmu_flag_get) for pmu_flag_get
    gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) refers to gd32f4xx_pmu.o(.bss) for reg_snap
    gd32f4xx_rcu.o(i.rcu_deinit) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_rcu.o(i.rcu_osci_stab_wait) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_second_adjust) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_i2s_clock_config) for rcu_i2s_clock_config
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_external_clock_mode0_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_clock_mode1_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_timer.o(i.timer_input_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_input_pwm_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_usart.o(i.usart_baudrate_set) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    system_gd32f4xx.o(i.SystemCoreClockUpdate) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i.system_clock_config) for system_clock_config
    system_gd32f4xx.o(i.system_clock_config) refers to system_gd32f4xx.o(i.system_clock_200m_25m_hxtal) for system_clock_200m_25m_hxtal
    startup_gd32f450.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450.o(RESET) refers to startup_gd32f450.o(STACK) for __initial_sp
    startup_gd32f450.o(RESET) refers to startup_gd32f450.o(.text) for Reset_Handler
    startup_gd32f450.o(RESET) refers to tim.o(i.TIMER0_BRK_TIMER8_IRQHandler) for TIMER0_BRK_TIMER8_IRQHandler
    startup_gd32f450.o(RESET) refers to tim.o(i.TIMER1_IRQHandler) for TIMER1_IRQHandler
    startup_gd32f450.o(RESET) refers to tim.o(i.TIMER2_IRQHandler) for TIMER2_IRQHandler
    startup_gd32f450.o(RESET) refers to tim.o(i.TIMER3_IRQHandler) for TIMER3_IRQHandler
    startup_gd32f450.o(RESET) refers to tim.o(i.TIMER4_IRQHandler) for TIMER4_IRQHandler
    startup_gd32f450.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450.o(.text) refers to system_gd32f4xx.o(i.SystemInit) for SystemInit
    startup_gd32f450.o(.text) refers to __main.o(!!!main) for __main
    startup_gd32f450.o(.text) refers to startup_gd32f450.o(HEAP) for Heap_Mem
    startup_gd32f450.o(.text) refers to startup_gd32f450.o(STACK) for Stack_Mem
    gpio.o(i.DO_Out) refers to gpio.o(i.DO16_Out) for DO16_Out
    gpio.o(i.DO_Out) refers to gpio.o(.data) for DO_LED_Run
    gpio.o(i.DO_Out) refers to gpio.o(.bss) for DO_Flag
    gpio.o(i.GPIO) refers to gpio.o(i.Refresh_DI) for Refresh_DI
    gpio.o(i.GPIO) refers to gpio.o(i.Refresh_Delayed_DI) for Refresh_Delayed_DI
    gpio.o(i.GPIO) refers to gpio.o(i.DO_Out) for DO_Out
    gpio.o(i.Init_DIDO_Para) refers to gpio.o(.bss) for DI_Flag
    gpio.o(i.Init_DIDO_Para) refers to gpio.o(.data) for DO_LED_Run
    gpio.o(i.Init_GPIO) refers to gpio.o(i.Init_DIDO_Para) for Init_DIDO_Para
    gpio.o(i.Init_GPIO) refers to gpio.o(i.GPIO_FSMC_Init) for GPIO_FSMC_Init
    gpio.o(i.Init_GPIO) refers to gpio.o(i.Refresh_DI) for Refresh_DI
    gpio.o(i.Init_GPIO) refers to gpio.o(i.Refresh_Delayed_DI) for Refresh_Delayed_DI
    gpio.o(i.Init_GPIO) refers to gpio.o(.data) for DO_LED_Run
    gpio.o(i.Refresh_DI) refers to gpio.o(i.DI16_Input) for DI16_Input
    gpio.o(i.Refresh_DI) refers to gpio.o(.bss) for DI_Flag
    gpio.o(i.Refresh_Delayed_DI) refers to gpio.o(i.Delayed_DI) for Delayed_DI
    gpio.o(i.Refresh_Delayed_DI) refers to gpio.o(.bss) for DI_Delay
    nandflash_sram.o(i.SRAM_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    nandflash_sram.o(i.SRAM_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    nandflash_sram.o(i.SRAM_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    nandflash_sram.o(i.SRAM_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    nandflash_sram.o(i.SRAM_Init) refers to gd32f4xx_exmc.o(i.exmc_norsram_init) for exmc_norsram_init
    nandflash_sram.o(i.SRAM_Init) refers to gd32f4xx_exmc.o(i.exmc_norsram_consecutive_clock_config) for exmc_norsram_consecutive_clock_config
    nandflash_sram.o(i.SRAM_Init) refers to gd32f4xx_exmc.o(i.exmc_norsram_enable) for exmc_norsram_enable
    optical_fiber.o(i.FPGA_DONE_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    optical_fiber.o(i.FPGA_DONE_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    optical_fiber.o(i.FPGA_RST_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    optical_fiber.o(i.FPGA_RST_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    optical_fiber.o(i.Optical_fiber_FSMC_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    optical_fiber.o(i.Optical_fiber_FSMC_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    optical_fiber.o(i.Optical_fiber_FSMC_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    optical_fiber.o(i.Optical_fiber_FSMC_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    optical_fiber.o(i.Optical_fiber_FSMC_Init) refers to gd32f4xx_exmc.o(i.exmc_norsram_init) for exmc_norsram_init
    optical_fiber.o(i.Optical_fiber_FSMC_Init) refers to gd32f4xx_exmc.o(i.exmc_norsram_consecutive_clock_config) for exmc_norsram_consecutive_clock_config
    optical_fiber.o(i.Optical_fiber_FSMC_Init) refers to gd32f4xx_exmc.o(i.exmc_norsram_enable) for exmc_norsram_enable
    optical_fiber.o(i.Optical_fiber_Init) refers to optical_fiber.o(i.Optical_fiber_FSMC_Init) for Optical_fiber_FSMC_Init
    optical_fiber.o(i.Optical_fiber_Init) refers to optical_fiber.o(i.FPGA_RST_Init) for FPGA_RST_Init
    optical_fiber.o(i.Optical_fiber_Init) refers to optical_fiber.o(i.FPGA_DONE_Init) for FPGA_DONE_Init
    optical_fiber.o(i.Optical_fiber_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    optical_fiber.o(i.Optical_fiber_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    optical_fiber.o(i.Optical_fiber_Init) refers to optical_fiber.o(i.BaudRate_Init) for BaudRate_Init
    optical_fiber.o(i.Optical_fiber_Init) refers to pub.o(.data) for uchNodeNum
    optical_fiber.o(i.Rx_Buffer) refers to optical_fiber.o(i.Read_State) for Read_State
    watchdog.o(i.WatchDog_Init) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    watchdog.o(i.WatchDog_Init) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    watchdog.o(i.WatchDog_Init) refers to gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear) for rcu_all_reset_flag_clear
    watchdog.o(i.WatchDog_Init) refers to gd32f4xx_fwdgt.o(i.fwdgt_write_enable) for fwdgt_write_enable
    watchdog.o(i.WatchDog_Init) refers to gd32f4xx_fwdgt.o(i.fwdgt_config) for fwdgt_config
    watchdog.o(i.WatchDog_Init) refers to gd32f4xx_fwdgt.o(i.fwdgt_counter_reload) for fwdgt_counter_reload
    watchdog.o(i.WatchDog_Init) refers to gd32f4xx_fwdgt.o(i.fwdgt_enable) for fwdgt_enable
    usartx.o(i.Init_RS232) refers to usartx.o(i.RS232_Init_Para) for RS232_Init_Para
    usartx.o(i.Init_RS232) refers to usartx.o(i.RS232_Init_Pin) for RS232_Init_Pin
    usartx.o(i.Init_RS232) refers to usartx.o(i.RS232_Init_Port) for RS232_Init_Port
    usartx.o(i.Init_RS232) refers to usartx.o(i.RS232_Init_DMA) for RS232_Init_DMA
    usartx.o(i.Init_RS485) refers to usartx.o(i.RS485_Init_Para) for RS485_Init_Para
    usartx.o(i.Init_RS485) refers to usartx.o(i.RS485_Init_Pin) for RS485_Init_Pin
    usartx.o(i.Init_RS485) refers to usartx.o(i.RS485_Init_Port) for RS485_Init_Port
    usartx.o(i.Init_RS485) refers to usartx.o(i.RS485_Init_DMA) for RS485_Init_DMA
    usartx.o(i.RS232_Init_DMA) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    usartx.o(i.RS232_Init_DMA) refers to usartx.o(i.RS232_Init_DMA_Tx) for RS232_Init_DMA_Tx
    usartx.o(i.RS232_Init_DMA) refers to usartx.o(i.RS232_Init_DMA_Rx) for RS232_Init_DMA_Rx
    usartx.o(i.RS232_Init_DMA_Rx) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    usartx.o(i.RS232_Init_DMA_Rx) refers to gd32f4xx_dma.o(i.dma_single_data_para_struct_init) for dma_single_data_para_struct_init
    usartx.o(i.RS232_Init_DMA_Rx) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    usartx.o(i.RS232_Init_DMA_Rx) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    usartx.o(i.RS232_Init_DMA_Rx) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    usartx.o(i.RS232_Init_DMA_Rx) refers to gd32f4xx_usart.o(i.usart_dma_receive_config) for usart_dma_receive_config
    usartx.o(i.RS232_Init_DMA_Rx) refers to usartx.o(.bss) for Usart1_Rx_Data
    usartx.o(i.RS232_Init_DMA_Tx) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    usartx.o(i.RS232_Init_DMA_Tx) refers to gd32f4xx_dma.o(i.dma_single_data_para_struct_init) for dma_single_data_para_struct_init
    usartx.o(i.RS232_Init_DMA_Tx) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    usartx.o(i.RS232_Init_DMA_Tx) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    usartx.o(i.RS232_Init_DMA_Tx) refers to gd32f4xx_usart.o(i.usart_dma_transmit_config) for usart_dma_transmit_config
    usartx.o(i.RS232_Init_DMA_Tx) refers to usartx.o(.bss) for Usart1_Tx_Data
    usartx.o(i.RS232_Init_DMA_Tx) refers to usartx.o(.data) for RS232_Tx_Num
    usartx.o(i.RS232_Init_Para) refers to usartx.o(.bss) for Usart1_Tx_Data
    usartx.o(i.RS232_Init_Para) refers to usartx.o(.data) for RxData
    usartx.o(i.RS232_Init_Pin) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    usartx.o(i.RS232_Init_Pin) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    usartx.o(i.RS232_Init_Pin) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    usartx.o(i.RS232_Init_Pin) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    usartx.o(i.RS232_Init_Port) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    usartx.o(i.RS232_Init_Port) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    usartx.o(i.RS232_Init_Port) refers to gd32f4xx_usart.o(i.usart_parity_config) for usart_parity_config
    usartx.o(i.RS232_Init_Port) refers to gd32f4xx_usart.o(i.usart_word_length_set) for usart_word_length_set
    usartx.o(i.RS232_Init_Port) refers to gd32f4xx_usart.o(i.usart_stop_bit_set) for usart_stop_bit_set
    usartx.o(i.RS232_Init_Port) refers to gd32f4xx_usart.o(i.usart_hardware_flow_cts_config) for usart_hardware_flow_cts_config
    usartx.o(i.RS232_Init_Port) refers to gd32f4xx_usart.o(i.usart_hardware_flow_rts_config) for usart_hardware_flow_rts_config
    usartx.o(i.RS232_Init_Port) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    usartx.o(i.RS232_Init_Port) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    usartx.o(i.RS232_Init_Port) refers to gd32f4xx_usart.o(i.usart_interrupt_disable) for usart_interrupt_disable
    usartx.o(i.RS232_Init_Port) refers to gd32f4xx_usart.o(i.usart_flag_clear) for usart_flag_clear
    usartx.o(i.RS232_Init_Port) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    usartx.o(i.RS232_Rx) refers to gd32f4xx_dma.o(i.dma_transfer_number_get) for dma_transfer_number_get
    usartx.o(i.RS232_Rx) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    usartx.o(i.RS232_Rx) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    usartx.o(i.RS232_Rx) refers to usartx.o(i.RS232_Init_DMA_Rx) for RS232_Init_DMA_Rx
    usartx.o(i.RS232_Rx) refers to usartx.o(.data) for RS232_Rx_Count
    usartx.o(i.RS232_Rx) refers to usartx.o(.bss) for Usart1_Rx_Data
    usartx.o(i.RS232_Tx) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    usartx.o(i.RS232_Tx) refers to gd32f4xx_usart.o(i.usart_dma_transmit_config) for usart_dma_transmit_config
    usartx.o(i.RS232_Tx) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    usartx.o(i.RS232_Tx) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    usartx.o(i.RS232_Tx) refers to usartx.o(i.RS232_Init_DMA_Tx) for RS232_Init_DMA_Tx
    usartx.o(i.RS232_Tx) refers to usartx.o(.data) for RS232_Tx_Enable_Flag
    usartx.o(i.RS232_Tx) refers to usartx.o(.bss) for Usart1_Tx_Data
    usartx.o(i.RS485_Init_DMA) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    usartx.o(i.RS485_Init_DMA) refers to usartx.o(i.RS485_Init_DMA_Tx) for RS485_Init_DMA_Tx
    usartx.o(i.RS485_Init_DMA) refers to usartx.o(i.RS485_Init_DMA_Rx) for RS485_Init_DMA_Rx
    usartx.o(i.RS485_Init_DMA_Rx) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    usartx.o(i.RS485_Init_DMA_Rx) refers to gd32f4xx_dma.o(i.dma_single_data_para_struct_init) for dma_single_data_para_struct_init
    usartx.o(i.RS485_Init_DMA_Rx) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    usartx.o(i.RS485_Init_DMA_Rx) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    usartx.o(i.RS485_Init_DMA_Rx) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    usartx.o(i.RS485_Init_DMA_Rx) refers to gd32f4xx_usart.o(i.usart_dma_receive_config) for usart_dma_receive_config
    usartx.o(i.RS485_Init_DMA_Rx) refers to usartx.o(.bss) for Usart2_Rx_Data
    usartx.o(i.RS485_Init_DMA_Tx) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    usartx.o(i.RS485_Init_DMA_Tx) refers to gd32f4xx_dma.o(i.dma_single_data_para_struct_init) for dma_single_data_para_struct_init
    usartx.o(i.RS485_Init_DMA_Tx) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    usartx.o(i.RS485_Init_DMA_Tx) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    usartx.o(i.RS485_Init_DMA_Tx) refers to gd32f4xx_usart.o(i.usart_dma_transmit_config) for usart_dma_transmit_config
    usartx.o(i.RS485_Init_DMA_Tx) refers to usartx.o(.bss) for Usart2_Tx_Data
    usartx.o(i.RS485_Init_DMA_Tx) refers to usartx.o(.data) for RS485_Tx_Num
    usartx.o(i.RS485_Init_Para) refers to usartx.o(.bss) for Usart2_Tx_Data
    usartx.o(i.RS485_Init_Para) refers to usartx.o(.data) for RS485_DMA_Tx_Flag
    usartx.o(i.RS485_Init_Pin) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    usartx.o(i.RS485_Init_Pin) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    usartx.o(i.RS485_Init_Pin) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    usartx.o(i.RS485_Init_Pin) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    usartx.o(i.RS485_Init_Port) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    usartx.o(i.RS485_Init_Port) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    usartx.o(i.RS485_Init_Port) refers to gd32f4xx_usart.o(i.usart_parity_config) for usart_parity_config
    usartx.o(i.RS485_Init_Port) refers to gd32f4xx_usart.o(i.usart_word_length_set) for usart_word_length_set
    usartx.o(i.RS485_Init_Port) refers to gd32f4xx_usart.o(i.usart_stop_bit_set) for usart_stop_bit_set
    usartx.o(i.RS485_Init_Port) refers to gd32f4xx_usart.o(i.usart_hardware_flow_cts_config) for usart_hardware_flow_cts_config
    usartx.o(i.RS485_Init_Port) refers to gd32f4xx_usart.o(i.usart_hardware_flow_rts_config) for usart_hardware_flow_rts_config
    usartx.o(i.RS485_Init_Port) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    usartx.o(i.RS485_Init_Port) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    usartx.o(i.RS485_Init_Port) refers to gd32f4xx_usart.o(i.usart_interrupt_disable) for usart_interrupt_disable
    usartx.o(i.RS485_Init_Port) refers to gd32f4xx_usart.o(i.usart_flag_clear) for usart_flag_clear
    usartx.o(i.RS485_Init_Port) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    usartx.o(i.RS485_Rx) refers to gd32f4xx_dma.o(i.dma_transfer_number_get) for dma_transfer_number_get
    usartx.o(i.RS485_Rx) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    usartx.o(i.RS485_Rx) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    usartx.o(i.RS485_Rx) refers to usartx.o(i.RS485_Init_DMA_Rx) for RS485_Init_DMA_Rx
    usartx.o(i.RS485_Rx) refers to usartx.o(.data) for RS485_Rx_Count
    usartx.o(i.RS485_Rx) refers to usartx.o(.bss) for Usart2_Rx_Data
    usartx.o(i.RS485_Tx) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    usartx.o(i.RS485_Tx) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    usartx.o(i.RS485_Tx) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    usartx.o(i.RS485_Tx) refers to gd32f4xx_usart.o(i.usart_dma_transmit_config) for usart_dma_transmit_config
    usartx.o(i.RS485_Tx) refers to usartx.o(i.RS485_Init_DMA_Tx) for RS485_Init_DMA_Tx
    usartx.o(i.RS485_Tx) refers to usartx.o(.data) for RS485_Tx_Enable_Flag
    usartx.o(i.RS485_Tx) refers to usartx.o(.bss) for Usart2_Tx_Data
    w25qxx.o(i.Start_W25QXX_Write) refers to w25qxx.o(.data) for W25QXX_Write_Flag
    w25qxx.o(i.W25QXX_EraseSector_Cycle) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    w25qxx.o(i.W25QXX_EraseSector_Cycle) refers to w25qxx.o(i.W25QXX_SPI_WriteByte) for W25QXX_SPI_WriteByte
    w25qxx.o(i.W25QXX_EraseSector_Cycle) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    w25qxx.o(i.W25QXX_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    w25qxx.o(i.W25QXX_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    w25qxx.o(i.W25QXX_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    w25qxx.o(i.W25QXX_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    w25qxx.o(i.W25QXX_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    w25qxx.o(i.W25QXX_Init) refers to gd32f4xx_spi.o(i.spi_i2s_deinit) for spi_i2s_deinit
    w25qxx.o(i.W25QXX_Init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    w25qxx.o(i.W25QXX_Init) refers to gd32f4xx_spi.o(i.spi_crc_polynomial_set) for spi_crc_polynomial_set
    w25qxx.o(i.W25QXX_Init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    w25qxx.o(i.W25QXX_Init) refers to w25qxx.o(i.W25QXX_Init_Flag) for W25QXX_Init_Flag
    w25qxx.o(i.W25QXX_Init_Flag) refers to w25qxx.o(.data) for W25QXX_Write_Flag
    w25qxx.o(i.W25QXX_ReInit) refers to w25qxx.o(i.W25QXX_Write_Enable) for W25QXX_Write_Enable
    w25qxx.o(i.W25QXX_ReInit) refers to w25qxx.o(.data) for W25QXX_Write_Addr_Once
    w25qxx.o(i.W25QXX_Read) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    w25qxx.o(i.W25QXX_Read) refers to w25qxx.o(i.W25QXX_SPI_WriteByte) for W25QXX_SPI_WriteByte
    w25qxx.o(i.W25QXX_Read) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    w25qxx.o(i.W25QXX_ReadId) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    w25qxx.o(i.W25QXX_ReadId) refers to w25qxx.o(i.W25QXX_SPI_WriteByte) for W25QXX_SPI_WriteByte
    w25qxx.o(i.W25QXX_ReadId) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    w25qxx.o(i.W25QXX_ReadSR) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    w25qxx.o(i.W25QXX_ReadSR) refers to w25qxx.o(i.W25QXX_SPI_WriteByte) for W25QXX_SPI_WriteByte
    w25qxx.o(i.W25QXX_ReadSR) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    w25qxx.o(i.W25QXX_SPI_WriteByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    w25qxx.o(i.W25QXX_SPI_WriteByte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    w25qxx.o(i.W25QXX_SPI_WriteByte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    w25qxx.o(i.W25QXX_Write) refers to w25qxx.o(i.W25QXX_Write_Enable) for W25QXX_Write_Enable
    w25qxx.o(i.W25QXX_Write) refers to w25qxx.o(i.W25QXX_ReadSR) for W25QXX_ReadSR
    w25qxx.o(i.W25QXX_Write) refers to w25qxx.o(i.W25QXX_EraseSector_Cycle) for W25QXX_EraseSector_Cycle
    w25qxx.o(i.W25QXX_Write) refers to w25qxx.o(i.sFLASH_WritePage_Cycle) for sFLASH_WritePage_Cycle
    w25qxx.o(i.W25QXX_Write) refers to w25qxx.o(i.W25QXX_Write_Verify) for W25QXX_Write_Verify
    w25qxx.o(i.W25QXX_Write) refers to w25qxx.o(.data) for W25QXX_Write_Buffer
    w25qxx.o(i.W25QXX_Write_Cycle) refers to w25qxx.o(i.W25QXX_Write_Enable) for W25QXX_Write_Enable
    w25qxx.o(i.W25QXX_Write_Cycle) refers to w25qxx.o(i.W25QXX_ReadSR) for W25QXX_ReadSR
    w25qxx.o(i.W25QXX_Write_Cycle) refers to w25qxx.o(i.W25QXX_EraseSector_Cycle) for W25QXX_EraseSector_Cycle
    w25qxx.o(i.W25QXX_Write_Cycle) refers to w25qxx.o(i.sFLASH_WritePage_Cycle) for sFLASH_WritePage_Cycle
    w25qxx.o(i.W25QXX_Write_Cycle) refers to w25qxx.o(i.W25QXX_Write_Verify) for W25QXX_Write_Verify
    w25qxx.o(i.W25QXX_Write_Cycle) refers to w25qxx.o(i.W25QXX_ReInit) for W25QXX_ReInit
    w25qxx.o(i.W25QXX_Write_Cycle) refers to w25qxx.o(i.W25QXX_Init_Flag) for W25QXX_Init_Flag
    w25qxx.o(i.W25QXX_Write_Cycle) refers to w25qxx.o(.data) for W25QXX_Write_Flag
    w25qxx.o(i.W25QXX_Write_Disable) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    w25qxx.o(i.W25QXX_Write_Disable) refers to w25qxx.o(i.W25QXX_SPI_WriteByte) for W25QXX_SPI_WriteByte
    w25qxx.o(i.W25QXX_Write_Disable) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    w25qxx.o(i.W25QXX_Write_Enable) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    w25qxx.o(i.W25QXX_Write_Enable) refers to w25qxx.o(i.W25QXX_SPI_WriteByte) for W25QXX_SPI_WriteByte
    w25qxx.o(i.W25QXX_Write_Enable) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    w25qxx.o(i.W25QXX_Write_Test) refers to w25qxx.o(i.W25QXX_Read) for W25QXX_Read
    w25qxx.o(i.W25QXX_Write_Test) refers to w25qxx.o(i.W25QXX_Write) for W25QXX_Write
    w25qxx.o(i.W25QXX_Write_Test) refers to w25qxx.o(.bss) for ucW
    w25qxx.o(i.W25QXX_Write_Verify) refers to w25qxx.o(i.W25QXX_Read) for W25QXX_Read
    w25qxx.o(i.W25QXX_Write_Verify) refers to w25qxx.o(.bss) for ucV
    w25qxx.o(i.sFLASH_WritePage_Cycle) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    w25qxx.o(i.sFLASH_WritePage_Cycle) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    w25qxx.o(i.sFLASH_WritePage_Cycle) refers to w25qxx.o(i.W25QXX_SPI_WriteByte) for W25QXX_SPI_WriteByte
    cl1606_spi.o(i.AD7606_PIN_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    cl1606_spi.o(i.AD7606_PIN_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    cl1606_spi.o(i.AD7606_PIN_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    cl1606_spi.o(i.AD7606_PIN_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    cl1606_spi.o(i.AD7606_PIN_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    cl1606_spi.o(i.AD7606_PIN_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    cl1606_spi.o(i.AD7606_PIN_Init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    cl1606_spi.o(i.AD7606_PIN_Init) refers to gd32f4xx_spi.o(i.spi_crc_polynomial_set) for spi_crc_polynomial_set
    cl1606_spi.o(i.AD7606_PIN_Init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    cl1606_spi.o(i.AD7606_Read_Reg) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    cl1606_spi.o(i.AD7606_Read_Reg) refers to cl1606_spi.o(i.SPIx_RW_One_Byte) for SPIx_RW_One_Byte
    cl1606_spi.o(i.AD7606_Read_Reg) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    cl1606_spi.o(i.AD7606_Read_Reg1) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    cl1606_spi.o(i.AD7606_Start_Convst) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    cl1606_spi.o(i.AD7606_Start_Convst) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    cl1606_spi.o(i.AD7606_Start_Convst) refers to cl1606_spi.o(i.Delay_Func) for Delay_Func
    cl1606_spi.o(i.AD7606_Start_Convst1) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    cl1606_spi.o(i.AD7606_Start_Convst1) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    cl1606_spi.o(i.AD7606_Start_Convst1) refers to cl1606_spi.o(i.Delay_Func) for Delay_Func
    cl1606_spi.o(i.CL1606_PIN_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    cl1606_spi.o(i.CL1606_PIN_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    cl1606_spi.o(i.CL1606_PIN_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    cl1606_spi.o(i.CL1606_PIN_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    cl1606_spi.o(i.CL1606_PIN_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    cl1606_spi.o(i.CL1606_PIN_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    cl1606_spi.o(i.CL1606_PIN_Init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    cl1606_spi.o(i.CL1606_PIN_Init) refers to gd32f4xx_spi.o(i.spi_crc_polynomial_set) for spi_crc_polynomial_set
    cl1606_spi.o(i.CL1606_PIN_Init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    cl1606_spi.o(i.Read_AD_Sample) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    cl1606_spi.o(i.Read_AD_Sample) refers to cl1606_spi.o(i.SCT_AD_R_Data) for SCT_AD_R_Data
    cl1606_spi.o(i.Read_AD_Sample) refers to cl1606_spi.o(i.AD7606_Start_Convst) for AD7606_Start_Convst
    cl1606_spi.o(i.Read_AD_Sample1) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    cl1606_spi.o(i.Read_AD_Sample1) refers to cl1606_spi.o(i.SCT_AD_R_Data1) for SCT_AD_R_Data1
    cl1606_spi.o(i.Read_AD_Sample1) refers to cl1606_spi.o(i.AD7606_Start_Convst1) for AD7606_Start_Convst1
    cl1606_spi.o(i.SCT_AD_Init) refers to cl1606_spi.o(i.CL1606_Data_Init) for CL1606_Data_Init
    cl1606_spi.o(i.SCT_AD_Init) refers to cl1606_spi.o(i.CL1606_PIN_Init) for CL1606_PIN_Init
    cl1606_spi.o(i.SCT_AD_Init) refers to cl1606_spi.o(i.CL1606_RST_Off) for CL1606_RST_Off
    cl1606_spi.o(i.SCT_AD_Init) refers to cl1606_spi.o(i.Delay_Func) for Delay_Func
    cl1606_spi.o(i.SCT_AD_R_Data) refers to cl1606_spi.o(i.AD7606_Read_Reg) for AD7606_Read_Reg
    cl1606_spi.o(i.SCT_AD_R_Data1) refers to cl1606_spi.o(i.AD7606_Read_Reg1) for AD7606_Read_Reg1
    cl1606_spi.o(i.SPIx_RW_One_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    cl1606_spi.o(i.SPIx_RW_One_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    cl1606_spi.o(i.SPIx_RW_One_Byte) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    cl1606_spi.o(i.SPIx_RW_One_Byte1) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    cl1606_spi.o(i.SPIx_RW_One_Byte1) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    cl1606_spi.o(i.SPIx_RW_One_Byte1) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    internet.o(i.Config_W5500) refers to internet.o(i.Write_SOCK_Byte1_W5500) for Write_SOCK_Byte1_W5500
    internet.o(i.Config_W5500) refers to internet.o(i.Write_SOCK_Byte2_W5500) for Write_SOCK_Byte2_W5500
    internet.o(i.Config_W5500) refers to internet.o(i.Write_Byte2_W5500) for Write_Byte2_W5500
    internet.o(i.Config_W5500) refers to internet.o(i.Write_Byte1_W5500) for Write_Byte1_W5500
    internet.o(i.Net_Comm) refers to internet.o(i.Net_Cycle) for Net_Cycle
    internet.o(i.Net_Comm) refers to internet.o(i.Net_DMA_Tx) for Net_DMA_Tx
    internet.o(i.Net_Comm) refers to internet.o(i.Net_DMA_Rx) for Net_DMA_Rx
    internet.o(i.Net_Comm) refers to internet.o(i.Net_Tx) for Net_Tx
    internet.o(i.Net_Comm) refers to internet.o(i.Net_Rx) for Net_Rx
    internet.o(i.Net_Comm) refers to internet.o(.data) for DMA_Tx_Size
    internet.o(i.Net_Cycle) refers to internet.o(i.Net_Mode_Select) for Net_Mode_Select
    internet.o(i.Net_Cycle) refers to internet.o(i.Write_SOCK_Byte1_W5500) for Write_SOCK_Byte1_W5500
    internet.o(i.Net_Cycle) refers to internet.o(i.Net_State_Check) for Net_State_Check
    internet.o(i.Net_Cycle) refers to internet.o(.data) for Net_Enable
    internet.o(i.Net_DMA_Rx) refers to internet.o(i.Net_DMA_rx_process) for Net_DMA_rx_process
    internet.o(i.Net_DMA_Rx) refers to internet.o(.data) for Net_State
    internet.o(i.Net_DMA_Tx) refers to internet.o(i.Net_DMA_tx_process) for Net_DMA_tx_process
    internet.o(i.Net_DMA_Tx) refers to internet.o(.data) for Net_State
    internet.o(i.Net_DMA_rx_process) refers to internet.o(i.Read_SOCK_Byte1_W5500) for Read_SOCK_Byte1_W5500
    internet.o(i.Net_DMA_rx_process) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    internet.o(i.Net_DMA_rx_process) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    internet.o(i.Net_DMA_rx_process) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    internet.o(i.Net_DMA_rx_process) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    internet.o(i.Net_DMA_rx_process) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    internet.o(i.Net_DMA_rx_process) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    internet.o(i.Net_DMA_rx_process) refers to gd32f4xx_spi.o(i.spi_disable) for spi_disable
    internet.o(i.Net_DMA_rx_process) refers to gd32f4xx_spi.o(i.spi_dma_disable) for spi_dma_disable
    internet.o(i.Net_DMA_rx_process) refers to internet.o(i.Write_SOCK_Byte1_W5500) for Write_SOCK_Byte1_W5500
    internet.o(i.Net_DMA_rx_process) refers to internet.o(.data) for Net_State
    internet.o(i.Net_DMA_rx_process) refers to internet.o(.bss) for SPI_Tx_Buf
    internet.o(i.Net_DMA_tx_process) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    internet.o(i.Net_DMA_tx_process) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    internet.o(i.Net_DMA_tx_process) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    internet.o(i.Net_DMA_tx_process) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    internet.o(i.Net_DMA_tx_process) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    internet.o(i.Net_DMA_tx_process) refers to gd32f4xx_spi.o(i.spi_disable) for spi_disable
    internet.o(i.Net_DMA_tx_process) refers to internet.o(i.Write_SOCK_Byte1_W5500) for Write_SOCK_Byte1_W5500
    internet.o(i.Net_DMA_tx_process) refers to internet.o(.data) for Net_State
    internet.o(i.Net_DMA_tx_process) refers to internet.o(.bss) for SPI_Tx_Buf
    internet.o(i.Net_Interrupt_Process) refers to internet.o(i.Read_Byte_W5500) for Read_Byte_W5500
    internet.o(i.Net_Interrupt_Process) refers to internet.o(i.Read_SOCK_Byte1_W5500) for Read_SOCK_Byte1_W5500
    internet.o(i.Net_Interrupt_Process) refers to internet.o(i.Write_SOCK_Byte1_W5500) for Write_SOCK_Byte1_W5500
    internet.o(i.Net_Interrupt_Process) refers to internet.o(i.Write_W5500) for Write_W5500
    internet.o(i.Net_Interrupt_Process) refers to internet.o(.data) for Net_Mode
    internet.o(i.Net_Interrupt_Process) refers to internet.o(.bss) for Net_Port
    internet.o(i.Net_Mode_Select) refers to internet.o(i.Socket_Mode) for Socket_Mode
    internet.o(i.Net_Mode_Select) refers to internet.o(i.Socket_Correct_Addr) for Socket_Correct_Addr
    internet.o(i.Net_Mode_Select) refers to internet.o(.data) for Net_Socket_State
    internet.o(i.Net_Para_Init) refers to internet.o(.data) for Net_State
    internet.o(i.Net_Rx) refers to internet.o(i.Read_SOCK_Byte2_W5500) for Read_SOCK_Byte2_W5500
    internet.o(i.Net_Rx) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    internet.o(i.Net_Rx) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    internet.o(i.Net_Rx) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    internet.o(i.Net_Rx) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    internet.o(i.Net_Rx) refers to gd32f4xx_spi.o(i.spi_disable) for spi_disable
    internet.o(i.Net_Rx) refers to internet.o(i.Write_SOCK_Byte2_W5500) for Write_SOCK_Byte2_W5500
    internet.o(i.Net_Rx) refers to internet.o(i.Write_SOCK_Byte1_W5500) for Write_SOCK_Byte1_W5500
    internet.o(i.Net_Rx) refers to internet.o(.data) for Net_Enable
    internet.o(i.Net_Rx) refers to internet.o(.bss) for Net_Rx_Buf
    internet.o(i.Net_State_Check) refers to internet.o(i.Net_Interrupt_Process) for Net_Interrupt_Process
    internet.o(i.Net_State_Check) refers to internet.o(.data) for Net_State
    internet.o(i.Net_Tx) refers to internet.o(i.Read_SOCK_Byte2_W5500) for Read_SOCK_Byte2_W5500
    internet.o(i.Net_Tx) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    internet.o(i.Net_Tx) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    internet.o(i.Net_Tx) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    internet.o(i.Net_Tx) refers to gd32f4xx_spi.o(i.spi_disable) for spi_disable
    internet.o(i.Net_Tx) refers to internet.o(i.Write_SOCK_Byte2_W5500) for Write_SOCK_Byte2_W5500
    internet.o(i.Net_Tx) refers to internet.o(i.Write_SOCK_Byte1_W5500) for Write_SOCK_Byte1_W5500
    internet.o(i.Net_Tx) refers to internet.o(.data) for Net_Enable
    internet.o(i.Network10_Disconnect) refers to internet.o(i.Write_SOCK_Byte1_W5500) for Write_SOCK_Byte1_W5500
    internet.o(i.Network10_Disconnect) refers to internet.o(.bss) for Net_IF0
    internet.o(i.Network1_Bind_Socket) refers to internet.o(i.Write_SOCK_Byte2_W5500) for Write_SOCK_Byte2_W5500
    internet.o(i.Network1_Bind_Socket) refers to internet.o(.bss) for Net_IF0
    internet.o(i.Network1_Disconnect) refers to internet.o(i.Write_SOCK_Byte1_W5500) for Write_SOCK_Byte1_W5500
    internet.o(i.Network1_Disconnect) refers to internet.o(i.Read_SOCK_Byte1_W5500) for Read_SOCK_Byte1_W5500
    internet.o(i.Network1_Disconnect) refers to internet.o(.bss) for Net_IF0
    internet.o(i.Network1_Get_Set_Remote_IP_Port_Length) refers to internet.o(i.Write_SOCK_Byte4_W5500) for Write_SOCK_Byte4_W5500
    internet.o(i.Network1_Get_Set_Remote_IP_Port_Length) refers to internet.o(i.Write_SOCK_Byte2_W5500) for Write_SOCK_Byte2_W5500
    internet.o(i.Network1_Get_Set_Remote_IP_Port_Length) refers to internet.o(.bss) for Net_IF0
    internet.o(i.Network1_Phy_Status) refers to internet.o(i.Read_Byte_W5500) for Read_Byte_W5500
    internet.o(i.Network1_Set_Remote_IP_Port) refers to internet.o(i.Write_SOCK_Byte4_W5500) for Write_SOCK_Byte4_W5500
    internet.o(i.Network1_Set_Remote_IP_Port) refers to internet.o(i.Write_SOCK_Byte2_W5500) for Write_SOCK_Byte2_W5500
    internet.o(i.Network1_Set_Remote_IP_Port) refers to internet.o(.bss) for Net_IF0
    internet.o(i.Network1_Setsockopt) refers to internet.o(i.Write_SOCK_Byte1_W5500) for Write_SOCK_Byte1_W5500
    internet.o(i.Network1_Setsockopt) refers to internet.o(.bss) for Net_IF0
    internet.o(i.Network20_Disconnect) refers to internet.o(i.Write_SOCK_Byte1_W5500) for Write_SOCK_Byte1_W5500
    internet.o(i.Network20_Disconnect) refers to internet.o(.bss) for Net_IF1
    internet.o(i.Network2_Bind_Socket) refers to internet.o(i.Write_SOCK_Byte2_W5500) for Write_SOCK_Byte2_W5500
    internet.o(i.Network2_Bind_Socket) refers to internet.o(.bss) for Net_IF1
    internet.o(i.Network2_Disconnect) refers to internet.o(i.Write_SOCK_Byte1_W5500) for Write_SOCK_Byte1_W5500
    internet.o(i.Network2_Disconnect) refers to internet.o(i.Read_SOCK_Byte1_W5500) for Read_SOCK_Byte1_W5500
    internet.o(i.Network2_Disconnect) refers to internet.o(.bss) for Net_IF1
    internet.o(i.Network2_Get_Set_Remote_IP_Port_Length) refers to internet.o(i.Write_SOCK_Byte4_W5500) for Write_SOCK_Byte4_W5500
    internet.o(i.Network2_Get_Set_Remote_IP_Port_Length) refers to internet.o(i.Write_SOCK_Byte2_W5500) for Write_SOCK_Byte2_W5500
    internet.o(i.Network2_Get_Set_Remote_IP_Port_Length) refers to internet.o(.bss) for Net_IF1
    internet.o(i.Network2_Phy_Status) refers to internet.o(i.Read_Byte_W5500) for Read_Byte_W5500
    internet.o(i.Network2_Set_Remote_IP_Port) refers to internet.o(i.Write_SOCK_Byte4_W5500) for Write_SOCK_Byte4_W5500
    internet.o(i.Network2_Set_Remote_IP_Port) refers to internet.o(i.Write_SOCK_Byte2_W5500) for Write_SOCK_Byte2_W5500
    internet.o(i.Network2_Set_Remote_IP_Port) refers to internet.o(.bss) for Net_IF1
    internet.o(i.Network2_Setsockopt) refers to internet.o(i.Write_SOCK_Byte1_W5500) for Write_SOCK_Byte1_W5500
    internet.o(i.Network2_Setsockopt) refers to internet.o(.bss) for Net_IF1
    internet.o(i.Network_Init) refers to internet.o(i.W5200U2_RST_Init) for W5200U2_RST_Init
    internet.o(i.Network_Init) refers to internet.o(i.SPI4_For_W5200_Init) for SPI4_For_W5200_Init
    internet.o(i.Network_Init) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    internet.o(i.Network_Init) refers to para.o(i.Soft_Delay) for Soft_Delay
    internet.o(i.Network_Init) refers to internet.o(i.W5200U2_RST_Off) for W5200U2_RST_Off
    internet.o(i.Network_Init) refers to internet.o(i.Write_Byte1_W5500) for Write_Byte1_W5500
    internet.o(i.Network_Init) refers to internet.o(i.Config_W5500) for Config_W5500
    internet.o(i.Network_Init) refers to internet.o(i.W5200U1_RST_Init) for W5200U1_RST_Init
    internet.o(i.Network_Init) refers to internet.o(i.SPI5_For_W5200_Init) for SPI5_For_W5200_Init
    internet.o(i.Network_Init) refers to internet.o(i.W5200U1_RST_Off) for W5200U1_RST_Off
    internet.o(i.Network_Init) refers to internet.o(i.SPI2_DMA_Config) for SPI2_DMA_Config
    internet.o(i.Network_Init) refers to internet.o(i.SPI1_DMA_Config) for SPI1_DMA_Config
    internet.o(i.Network_Init) refers to internet.o(i.Write_W5500) for Write_W5500
    internet.o(i.Network_Init) refers to internet.o(i.Write_SOCK_Byte2_W5500) for Write_SOCK_Byte2_W5500
    internet.o(i.Network_Init) refers to internet.o(i.Write_SOCK_Byte4_W5500) for Write_SOCK_Byte4_W5500
    internet.o(i.Network_Init) refers to internet.o(i.Net_Para_Init) for Net_Para_Init
    internet.o(i.Network_Init) refers to internet.o(.data) for DMA_Tx_Size
    internet.o(i.Network_Init) refers to pub.o(.data) for uchNodeNum
    internet.o(i.Network_Init) refers to internet.o(.bss) for Net_Port
    internet.o(i.Read_Byte_W5500) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    internet.o(i.Read_Byte_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    internet.o(i.Read_Byte_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    internet.o(i.Read_Byte_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    internet.o(i.Read_Byte_W5500) refers to gd32f4xx_spi.o(i.spi_disable) for spi_disable
    internet.o(i.Read_Byte_W5500) refers to internet.o(i.Delay_W5100) for Delay_W5100
    internet.o(i.Read_SOCK_Byte1_W5500) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    internet.o(i.Read_SOCK_Byte1_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    internet.o(i.Read_SOCK_Byte1_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    internet.o(i.Read_SOCK_Byte1_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    internet.o(i.Read_SOCK_Byte1_W5500) refers to gd32f4xx_spi.o(i.spi_disable) for spi_disable
    internet.o(i.Read_SOCK_Byte1_W5500) refers to internet.o(i.Delay_W5100) for Delay_W5100
    internet.o(i.Read_SOCK_Byte2_W5500) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    internet.o(i.Read_SOCK_Byte2_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    internet.o(i.Read_SOCK_Byte2_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    internet.o(i.Read_SOCK_Byte2_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    internet.o(i.Read_SOCK_Byte2_W5500) refers to gd32f4xx_spi.o(i.spi_disable) for spi_disable
    internet.o(i.Read_SOCK_Byte2_W5500) refers to internet.o(i.Delay_W5100) for Delay_W5100
    internet.o(i.Read_SOCK_ByteX_W5500) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    internet.o(i.Read_SOCK_ByteX_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    internet.o(i.Read_SOCK_ByteX_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    internet.o(i.Read_SOCK_ByteX_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    internet.o(i.Read_SOCK_ByteX_W5500) refers to gd32f4xx_spi.o(i.spi_disable) for spi_disable
    internet.o(i.Read_SOCK_ByteX_W5500) refers to internet.o(i.Delay_W5100) for Delay_W5100
    internet.o(i.Read_W5500) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    internet.o(i.Read_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    internet.o(i.Read_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    internet.o(i.Read_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_data_receive) for spi_i2s_data_receive
    internet.o(i.Read_W5500) refers to gd32f4xx_spi.o(i.spi_disable) for spi_disable
    internet.o(i.Read_W5500) refers to internet.o(i.Delay_W5100) for Delay_W5100
    internet.o(i.SPI1_DMA_Config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    internet.o(i.SPI1_DMA_Config) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    internet.o(i.SPI1_DMA_Config) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    internet.o(i.SPI1_DMA_Config) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    internet.o(i.SPI1_DMA_Config) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    internet.o(i.SPI1_DMA_Config) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    internet.o(i.SPI1_DMA_Config) refers to gd32f4xx_dma.o(i.dma_single_data_para_struct_init) for dma_single_data_para_struct_init
    internet.o(i.SPI1_DMA_Config) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    internet.o(i.SPI1_DMA_Config) refers to internet.o(.bss) for SPI_Tx_Buf
    internet.o(i.SPI1_DMA_Config) refers to internet.o(.data) for DMA_Tx_Size
    internet.o(i.SPI2_DMA_Config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    internet.o(i.SPI2_DMA_Config) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    internet.o(i.SPI2_DMA_Config) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    internet.o(i.SPI2_DMA_Config) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    internet.o(i.SPI2_DMA_Config) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    internet.o(i.SPI2_DMA_Config) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    internet.o(i.SPI2_DMA_Config) refers to gd32f4xx_dma.o(i.dma_single_data_para_struct_init) for dma_single_data_para_struct_init
    internet.o(i.SPI2_DMA_Config) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    internet.o(i.SPI2_DMA_Config) refers to internet.o(.bss) for SPI_Tx_Buf
    internet.o(i.SPI2_DMA_Config) refers to internet.o(.data) for DMA_Tx_Size
    internet.o(i.SPI4_For_W5200_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    internet.o(i.SPI4_For_W5200_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    internet.o(i.SPI4_For_W5200_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    internet.o(i.SPI4_For_W5200_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    internet.o(i.SPI4_For_W5200_Init) refers to gd32f4xx_spi.o(i.spi_i2s_deinit) for spi_i2s_deinit
    internet.o(i.SPI4_For_W5200_Init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    internet.o(i.SPI4_For_W5200_Init) refers to gd32f4xx_spi.o(i.spi_crc_polynomial_set) for spi_crc_polynomial_set
    internet.o(i.SPI4_For_W5200_Init) refers to gd32f4xx_spi.o(i.spi_disable) for spi_disable
    internet.o(i.SPI4_For_W5200_Init) refers to gd32f4xx_spi.o(i.spi_nss_output_enable) for spi_nss_output_enable
    internet.o(i.SPI5_For_W5200_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    internet.o(i.SPI5_For_W5200_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    internet.o(i.SPI5_For_W5200_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    internet.o(i.SPI5_For_W5200_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    internet.o(i.SPI5_For_W5200_Init) refers to gd32f4xx_spi.o(i.spi_i2s_deinit) for spi_i2s_deinit
    internet.o(i.SPI5_For_W5200_Init) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    internet.o(i.SPI5_For_W5200_Init) refers to gd32f4xx_spi.o(i.spi_crc_polynomial_set) for spi_crc_polynomial_set
    internet.o(i.SPI5_For_W5200_Init) refers to gd32f4xx_spi.o(i.spi_disable) for spi_disable
    internet.o(i.SPI5_For_W5200_Init) refers to gd32f4xx_spi.o(i.spi_nss_output_enable) for spi_nss_output_enable
    internet.o(i.SocketU1_Connect) refers to internet.o(i.W5500_SOCK_Connect) for W5500_SOCK_Connect
    internet.o(i.SocketU1_Listen) refers to internet.o(i.W5500_SOCK_Listen) for W5500_SOCK_Listen
    internet.o(i.SocketU2_Connect) refers to internet.o(i.W5500_SOCK_Connect) for W5500_SOCK_Connect
    internet.o(i.SocketU2_Listen) refers to internet.o(i.W5500_SOCK_Listen) for W5500_SOCK_Listen
    internet.o(i.Socket_Correct_Addr) refers to internet.o(i.Read_SOCK_Byte1_W5500) for Read_SOCK_Byte1_W5500
    internet.o(i.Socket_Correct_Addr) refers to internet.o(i.Write_W5500) for Write_W5500
    internet.o(i.Socket_Correct_Addr) refers to internet.o(i.Write_SOCK_Byte1_W5500) for Write_SOCK_Byte1_W5500
    internet.o(i.Socket_Correct_Addr) refers to internet.o(.data) for DMA_Tx_Size
    internet.o(i.Socket_Correct_Addr) refers to internet.o(.bss) for SPI_Tx_Buf
    internet.o(i.Socket_Mode) refers to internet.o(i.Write_SOCK_Byte1_W5500) for Write_SOCK_Byte1_W5500
    internet.o(i.Socket_Mode) refers to internet.o(i.Read_SOCK_Byte1_W5500) for Read_SOCK_Byte1_W5500
    internet.o(i.W5200U1_RST_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    internet.o(i.W5200U1_RST_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    internet.o(i.W5200U1_RST_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    internet.o(i.W5200U1_RST_Init) refers to internet.o(i.W5200U1_RST_Off) for W5200U1_RST_Off
    internet.o(i.W5200U1_RST_Off) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    internet.o(i.W5200U2_RST_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    internet.o(i.W5200U2_RST_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    internet.o(i.W5200U2_RST_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    internet.o(i.W5200U2_RST_Init) refers to internet.o(i.W5200U2_RST_Off) for W5200U2_RST_Off
    internet.o(i.W5200U2_RST_Off) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    internet.o(i.W5500_SOCK_Connect) refers to internet.o(i.Write_SOCK_Byte1_W5500) for Write_SOCK_Byte1_W5500
    internet.o(i.W5500_SOCK_Connect) refers to internet.o(i.Read_SOCK_Byte1_W5500) for Read_SOCK_Byte1_W5500
    internet.o(i.W5500_SOCK_Listen) refers to internet.o(i.Write_SOCK_Byte1_W5500) for Write_SOCK_Byte1_W5500
    internet.o(i.W5500_SOCK_Listen) refers to internet.o(i.Read_SOCK_Byte1_W5500) for Read_SOCK_Byte1_W5500
    internet.o(i.Write_Byte1_W5500) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    internet.o(i.Write_Byte1_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    internet.o(i.Write_Byte1_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    internet.o(i.Write_Byte1_W5500) refers to gd32f4xx_spi.o(i.spi_disable) for spi_disable
    internet.o(i.Write_Byte1_W5500) refers to internet.o(i.Delay_W5100) for Delay_W5100
    internet.o(i.Write_Byte2_W5500) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    internet.o(i.Write_Byte2_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    internet.o(i.Write_Byte2_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    internet.o(i.Write_Byte2_W5500) refers to gd32f4xx_spi.o(i.spi_disable) for spi_disable
    internet.o(i.Write_Byte2_W5500) refers to internet.o(i.Delay_W5100) for Delay_W5100
    internet.o(i.Write_SOCK_Byte1_W5500) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    internet.o(i.Write_SOCK_Byte1_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    internet.o(i.Write_SOCK_Byte1_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    internet.o(i.Write_SOCK_Byte1_W5500) refers to gd32f4xx_spi.o(i.spi_disable) for spi_disable
    internet.o(i.Write_SOCK_Byte1_W5500) refers to internet.o(i.Delay_W5100) for Delay_W5100
    internet.o(i.Write_SOCK_Byte2_W5500) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    internet.o(i.Write_SOCK_Byte2_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    internet.o(i.Write_SOCK_Byte2_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    internet.o(i.Write_SOCK_Byte2_W5500) refers to gd32f4xx_spi.o(i.spi_disable) for spi_disable
    internet.o(i.Write_SOCK_Byte2_W5500) refers to internet.o(i.Delay_W5100) for Delay_W5100
    internet.o(i.Write_SOCK_Byte4_W5500) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    internet.o(i.Write_SOCK_Byte4_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    internet.o(i.Write_SOCK_Byte4_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    internet.o(i.Write_SOCK_Byte4_W5500) refers to gd32f4xx_spi.o(i.spi_disable) for spi_disable
    internet.o(i.Write_SOCK_Byte4_W5500) refers to internet.o(i.Delay_W5100) for Delay_W5100
    internet.o(i.Write_W5500) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    internet.o(i.Write_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_data_transmit) for spi_i2s_data_transmit
    internet.o(i.Write_W5500) refers to gd32f4xx_spi.o(i.spi_i2s_flag_get) for spi_i2s_flag_get
    internet.o(i.Write_W5500) refers to gd32f4xx_spi.o(i.spi_disable) for spi_disable
    internet.o(i.Write_W5500) refers to internet.o(i.Delay_W5100) for Delay_W5100
    ai.o(i.AD) refers to cl1606_spi.o(i.Read_AD_Sample) for Read_AD_Sample
    ai.o(i.AD1) refers to cl1606_spi.o(i.Read_AD_Sample1) for Read_AD_Sample1
    ai.o(i.AD1) refers to ai.o(.bss) for AD_Data1
    ai.o(i.Cal_AD) refers to ai.o(i.Cal_Arm_I) for Cal_Arm_I
    ai.o(i.Cal_AD) refers to ai.o(i.RMS) for RMS
    ai.o(i.Cal_AD) refers to ai.o(i.Get_Medium_Value) for Get_Medium_Value
    ai.o(i.Cal_AD) refers to pub.o(.data) for AI_Flag
    ai.o(i.Cal_AD) refers to ai.o(.bss) for ADC_Data
    ai.o(i.Cal_AD) refers to pub.o(.bss) for Iarm
    ai.o(i.Cal_Arm_I) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    ai.o(i.Cal_Arm_I) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    ai.o(i.Cal_Arm_I) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ai.o(i.Cal_Arm_I) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    ai.o(i.Cal_Arm_I) refers to pub.o(.data) for I_AD_Const
    ai.o(i.Cal_Avg) refers to pub.o(.data) for Vabc
    ai.o(i.Get_Monitor) refers to pub.o(.data) for Net_Monitor_C
    ai.o(i.Get_Monitor) refers to pub.o(.bss) for ucMornitor
    ai.o(i.Get_Monitor) refers to internet.o(.data) for Net_Tx_Flag
    ai.o(i.Init_AD_Data) refers to ai.o(i.Init_Avg) for Init_Avg
    ai.o(i.Init_AD_Data) refers to ai.o(.bss) for V
    ai.o(i.Init_AD_Data) refers to ai.o(.data) for AI_Count
    ai.o(i.Init_AD_Data) refers to pub.o(.data) for Va
    ai.o(i.Init_AD_Data) refers to pub.o(.bss) for Iarm
    ai.o(i.Init_Avg) refers to pub.o(.data) for Vav
    ai.o(i.RMS) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    ai.o(i.RMS) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    ai.o(i.RMS) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    ai.o(i.Shift_AD_Data) refers to ai.o(.bss) for ADC_Data
    ai.o(i.Zero_Calibrate) refers to pub.o(.data) for Pulse_Enabled_Flag
    ai.o(i.Zero_Calibrate) refers to ai.o(.bss) for ADC_Data
    ai.o(i.Zero_Calibrate) refers to pub.o(.bss) for AI_Offset
    command.o(i.Init_RS232_Comm) refers to usartx.o(i.Init_RS232) for Init_RS232
    command.o(i.Init_RS232_Comm) refers to command.o(i.Init_USART_Data) for Init_USART_Data
    command.o(i.Init_USART_Data) refers to pub.o(.data) for Asyn_Tx_Flag
    command.o(i.Init_USART_Data) refers to pub.o(.bss) for AsynTxData
    command.o(i.Monitor_Command) refers to optical.o(i.CRC16) for CRC16
    command.o(i.Monitor_Command) refers to optical.o(i.Byte_To_Float) for Byte_To_Float
    command.o(i.Monitor_Command) refers to pub.o(.bss) for AsynRxData
    command.o(i.Monitor_Command) refers to pub.o(.data) for UI_Ref_Up_Flag
    command.o(i.RS232_Comm) refers to command.o(i.Status_Data) for Status_Data
    command.o(i.RS232_Comm) refers to usartx.o(i.RS232_Tx) for RS232_Tx
    command.o(i.RS232_Comm) refers to usartx.o(i.RS232_Rx) for RS232_Rx
    command.o(i.RS232_Comm) refers to command.o(i.Monitor_Command) for Monitor_Command
    command.o(i.RS232_Comm) refers to pub.o(.data) for Status_Delay
    command.o(i.RS232_Comm) refers to pub.o(.bss) for AsynTxBuf
    command.o(i.Status_Data) refers to optical.o(i.Float_To_Byte) for Float_To_Byte
    command.o(i.Status_Data) refers to optical.o(i.CRC16) for CRC16
    command.o(i.Status_Data) refers to pub.o(.data) for Asyn_Tx_Flag
    command.o(i.Status_Data) refers to pub.o(.bss) for AsynTxBuf
    dido.o(i.Breaker_Check) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    dido.o(i.Breaker_Check) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    dido.o(i.Breaker_Check) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    dido.o(i.Breaker_Check) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    dido.o(i.Breaker_Check) refers to pub.o(.data) for P
    dido.o(i.DI) refers to gpio.o(i.Refresh_DI) for Refresh_DI
    dido.o(i.DI) refers to gpio.o(i.Refresh_Delayed_DI) for Refresh_Delayed_DI
    dido.o(i.DI) refers to dido.o(i.Get_DI1) for Get_DI1
    dido.o(i.DI) refers to dido.o(i.Get_DI2) for Get_DI2
    dido.o(i.DIDO) refers to dido.o(i.DI) for DI
    dido.o(i.DIDO) refers to dido.o(i.DO) for DO
    dido.o(i.DO) refers to dido.o(i.Refresh_DO_Flag) for Refresh_DO_Flag
    dido.o(i.DO) refers to dido.o(i.DO1) for DO1
    dido.o(i.DO) refers to dido.o(i.DO2) for DO2
    dido.o(i.DO) refers to dido.o(i.DO3) for DO3
    dido.o(i.DO) refers to dido.o(i.DO4) for DO4
    dido.o(i.DO) refers to dido.o(i.DO5) for DO5
    dido.o(i.DO) refers to dido.o(i.DO6) for DO6
    dido.o(i.DO) refers to gpio.o(i.DO_Out) for DO_Out
    dido.o(i.DO) refers to pub.o(.data) for SynSource
    dido.o(i.DO1) refers to pub.o(.data) for SCR_Error_Flag
    dido.o(i.DO1) refers to gpio.o(.bss) for DO_Flag
    dido.o(i.DO2) refers to pub.o(.data) for ROM_Flag
    dido.o(i.DO2) refers to gpio.o(.bss) for DO_Flag
    dido.o(i.DO3) refers to optical.o(.bss) for Optical_Comm_Status
    dido.o(i.DO3) refers to pub.o(.data) for Low_F_Flag
    dido.o(i.DO3) refers to gpio.o(.bss) for DO_Flag
    dido.o(i.DO4) refers to pub.o(.data) for Online_Flag
    dido.o(i.DO4) refers to optical.o(.data) for Online_Source
    dido.o(i.DO5) refers to optical.o(.bss) for Optical_Comm_Status
    dido.o(i.DO5) refers to pub.o(.data) for DO_Data
    dido.o(i.DO6) refers to optical.o(.bss) for Optical_Comm_Status
    dido.o(i.DO6) refers to pub.o(.data) for AVR_Flag
    dido.o(i.DO_Lock_Check) refers to pub.o(.data) for DO_Lock_Delay
    dido.o(i.Get_DI1) refers to gpio.o(.bss) for DI_Delayed_Flag
    dido.o(i.Get_DI1) refers to pub.o(.data) for Fuse_Flag
    dido.o(i.Get_DI2) refers to dido.o(i.Breaker_Check) for Breaker_Check
    dido.o(i.Get_DI2) refers to gpio.o(.bss) for DI_Delayed_Flag
    dido.o(i.Get_DI2) refers to pub.o(.data) for Pulse_Enabled_Flag
    dido.o(i.Low_F_Check) refers to syn.o(i.Syn_Pulse_Init) for Syn_Pulse_Init
    dido.o(i.Low_F_Check) refers to dido.o(i.Low_F_Trigger) for Low_F_Trigger
    dido.o(i.Low_F_Check) refers to pub.o(.data) for Low_F_Count
    dido.o(i.Low_F_Trigger) refers to tim.o(i.Set_Alpha_Delay) for Set_Alpha_Delay
    dido.o(i.Low_F_Trigger) refers to pub.o(.data) for Low_F_Flag
    dido.o(i.Low_F_Trigger) refers to optical.o(.data) for Optical_Tx_Flag
    dido.o(i.Refresh_DO_Flag) refers to dido.o(i.Running_Signal) for Running_Signal
    dido.o(i.Refresh_DO_Flag) refers to dido.o(i.DO_Lock_Check) for DO_Lock_Check
    dido.o(i.Refresh_DO_Flag) refers to dido.o(i.SCR_Check) for SCR_Check
    dido.o(i.Refresh_DO_Flag) refers to dido.o(i.Low_F_Check) for Low_F_Check
    dido.o(i.Running_Signal) refers to pub.o(.data) for Running_Count
    dido.o(i.SCR_Check) refers to pub.o(.data) for DO_Lock_Flag
    dido.o(i.SCR_Check) refers to optical.o(.data) for Optical_Comm_Err_Flag
    eifd.o(i.AVR) refers to eifd.o(i.PID) for PID
    eifd.o(i.AVR) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    eifd.o(i.AVR) refers to acos.o(i.__hardfp_acos) for __hardfp_acos
    eifd.o(i.AVR) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    eifd.o(i.AVR) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    eifd.o(i.AVR) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    eifd.o(i.AVR) refers to eifd.o(.data) for PID_Cycle_C
    eifd.o(i.AVR) refers to pub.o(.data) for Ref_Ctrl
    eifd.o(i.AVR) refers to pub.o(.bss) for PID_T
    eifd.o(i.Init_PID) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    eifd.o(i.Init_PID) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    eifd.o(i.Init_PID) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    eifd.o(i.Init_PID) refers to eifd.o(.data) for Vamax
    eifd.o(i.Init_PID) refers to pub.o(.data) for AVR_Flag
    eifd.o(i.PID) refers to eifd.o(.data) for Vamax
    eifd.o(i.PID) refers to pub.o(.data) for PID_X
    limit.o(i.Init_Limit) refers to pub.o(.data) for PT_Break_Flag
    limit.o(i.Limit_Check) refers to limit.o(i.Over_Ex_Check) for Over_Ex_Check
    limit.o(i.Limit_Check) refers to limit.o(i.Scr_Exit_Check) for Scr_Exit_Check
    limit.o(i.Limit_Check) refers to limit.o(i.PT_Check) for PT_Check
    limit.o(i.Limit_Check) refers to dido.o(i.Low_F_Check) for Low_F_Check
    limit.o(i.Limit_Check) refers to syn.o(i.Pulse_Syn_Low_F_Check) for Pulse_Syn_Low_F_Check
    limit.o(i.Limit_Check) refers to pub.o(.data) for Pulse_Enabled_Flag
    limit.o(i.Over_Ex_Check) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    limit.o(i.Over_Ex_Check) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    limit.o(i.Over_Ex_Check) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    limit.o(i.Over_Ex_Check) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    limit.o(i.Over_Ex_Check) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    limit.o(i.Over_Ex_Check) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    limit.o(i.Over_Ex_Check) refers to pub.o(.data) for Ifd_Sum
    limit.o(i.PT_Check) refers to mode.o(i.Work_Mode_Ifd) for Work_Mode_Ifd
    limit.o(i.PT_Check) refers to mode.o(i.Work_Mode_V) for Work_Mode_V
    limit.o(i.PT_Check) refers to pub.o(.data) for Vav
    limit.o(i.Scr_Exit_Check) refers to mode.o(i.Work_Mode_Ifd) for Work_Mode_Ifd
    limit.o(i.Scr_Exit_Check) refers to mode.o(i.Work_Mode_V) for Work_Mode_V
    limit.o(i.Scr_Exit_Check) refers to optical.o(.bss) for Scr_Comm_OK_Num
    limit.o(i.Scr_Exit_Check) refers to pub.o(.data) for uchNodeNum
    main.o(i.main) refers to main.o(i.Boot_Delay) for Boot_Delay
    main.o(i.main) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    main.o(i.main) refers to para.o(i.Init_Hardware) for Init_Hardware
    main.o(i.main) refers to para.o(i.Init_Data) for Init_Data
    main.o(i.main) refers to optical.o(i.Dyn_Comp_Timer_Init) for Dyn_Comp_Timer_Init
    main.o(i.main) refers to net.o(i.Net_Check) for Net_Check
    mode.o(i.Work_Mode_Ifd) refers to pub.o(.data) for AVR_Flag
    mode.o(i.Work_Mode_V) refers to pub.o(.data) for AVR_Flag
    mode.o(i.Work_Mode_Vfd) refers to pub.o(.data) for AVR_Flag
    net.o(i.Init_Net) refers to internet.o(i.Network_Init) for Network_Init
    net.o(i.Init_Net) refers to pub.o(.data) for uchNodeNum
    net.o(i.Net_Check) refers to optical.o(i.Byte_To_Float) for Byte_To_Float
    net.o(i.Net_Check) refers to net.o(i.Net_Order) for Net_Order
    net.o(i.Net_Check) refers to internet.o(i.Net_Comm) for Net_Comm
    net.o(i.Net_Check) refers to internet.o(.data) for Net_Rx_Flag
    net.o(i.Net_Check) refers to internet.o(.bss) for Net_Rx_Buf
    net.o(i.Net_Check) refers to pub.o(.data) for P
    net.o(i.Net_Check) refers to pub.o(.bss) for ucQTx
    net.o(i.Net_Order) refers to internet.o(.bss) for Net_IF0
    net.o(i.Net_Order) refers to net.o(.data) for Net_Tx_Count
    net.o(i.Net_Order) refers to internet.o(.data) for Net_Tx_size
    net.o(i.Net_Order) refers to pub.o(.bss) for ucQTx
    optical.o(i.CRC16) refers to optical.o(.data) for m_auchCRCHi
    optical.o(i.Calc_Update_Flag) refers to pub.o(.data) for Sec10Start
    optical.o(i.Clear_Comm_Err_Module) refers to optical.o(.data) for Online_Source
    optical.o(i.Clear_Comm_Err_Module) refers to optical.o(.bss) for Optical_PID_X
    optical.o(i.Collect_Sensor_ID) refers to optical.o(i.CRC16) for CRC16
    optical.o(i.Collect_Sensor_ID) refers to pub.o(.bss) for Sensor_ID
    optical.o(i.Collect_Sensor_ID) refers to optical.o(.bss) for OpticalTxData
    optical.o(i.Collect_Sensor_ID) refers to pub.o(.data) for uchNodeNum
    optical.o(i.Dyn_Comp_Timer_Init) refers to pub.o(.data) for Sec10Start
    optical.o(i.Ex_Data_Optical) refers to optical.o(i.Byte_To_Float) for Byte_To_Float
    optical.o(i.Ex_Data_Optical) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    optical.o(i.Ex_Data_Optical) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    optical.o(i.Ex_Data_Optical) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    optical.o(i.Ex_Data_Optical) refers to optical.o(i.Get_Command) for Get_Command
    optical.o(i.Ex_Data_Optical) refers to pub.o(.data) for uchNodeNum
    optical.o(i.Ex_Data_Optical) refers to optical.o(.bss) for Optical_Comm_Status
    optical.o(i.Ex_Data_Optical) refers to optical.o(.data) for Optical_Pulse_Flag
    optical.o(i.Ex_Data_Sensor_ID) refers to pub.o(.bss) for Sensor_ID
    optical.o(i.Ex_Data_Sensor_ID) refers to pub.o(.data) for uchNodeNum
    optical.o(i.Get_Command) refers to optical.o(i.Pulse_Test) for Pulse_Test
    optical.o(i.Get_Command) refers to mode.o(i.Work_Mode_V) for Work_Mode_V
    optical.o(i.Get_Command) refers to mode.o(i.Work_Mode_Ifd) for Work_Mode_Ifd
    optical.o(i.Get_Command) refers to optical.o(i.Get_eIfd_Command) for Get_eIfd_Command
    optical.o(i.Get_Command) refers to ai.o(i.Zero_Calibrate) for Zero_Calibrate
    optical.o(i.Get_Command) refers to para.o(i.Write_ROM) for Write_ROM
    optical.o(i.Get_Command) refers to optical.o(i.Byte_To_Float) for Byte_To_Float
    optical.o(i.Get_Command) refers to pub.o(.data) for uchNodeNum
    optical.o(i.Get_Command) refers to optical.o(.data) for ROM_Write_Result
    optical.o(i.Get_Command) refers to pub.o(.bss) for PID_T
    optical.o(i.Get_eIfd_Command) refers to pub.o(.bss) for eIfd_Count
    optical.o(i.Init_Optical_Comm) refers to optical.o(.bss) for OpticalRxData
    optical.o(i.Init_Optical_Comm) refers to optical.o(.data) for Optical_Tx_Flag
    optical.o(i.Init_Optical_Comm) refers to pub.o(.data) for uchNodeNum
    optical.o(i.Init_Optical_Comm) refers to pub.o(.bss) for Scr_Pulse_Count
    optical.o(i.Online_Check) refers to optical.o(i.SelectSynSource) for SelectSynSource
    optical.o(i.Online_Check) refers to syn.o(i.SelectSyn) for SelectSyn
    optical.o(i.Online_Check) refers to pub.o(.data) for DO_Lock_Flag
    optical.o(i.Online_Check) refers to optical.o(.data) for Online_Source
    optical.o(i.Online_Check) refers to optical.o(.bss) for Scr_Comm_OK_Num
    optical.o(i.Optical_Comm) refers to optical_fiber.o(i.Rx_Buffer) for Rx_Buffer
    optical.o(i.Optical_Comm) refers to optical.o(i.Search_Command) for Search_Command
    optical.o(i.Optical_Comm) refers to optical.o(i.Clear_Comm_Err_Module) for Clear_Comm_Err_Module
    optical.o(i.Optical_Comm) refers to optical.o(i.SCR_Data_Optical) for SCR_Data_Optical
    optical.o(i.Optical_Comm) refers to optical.o(i.Optical_Comm_Check) for Optical_Comm_Check
    optical.o(i.Optical_Comm) refers to optical_fiber.o(i.Tx_Buffer) for Tx_Buffer
    optical.o(i.Optical_Comm) refers to optical.o(i.Collect_Sensor_ID) for Collect_Sensor_ID
    optical.o(i.Optical_Comm) refers to pub.o(.data) for uchNodeNum
    optical.o(i.Optical_Comm) refers to optical.o(.bss) for OpticalRxData
    optical.o(i.Optical_Comm) refers to optical.o(.data) for Optical_Rx_Flag
    optical.o(i.Optical_Comm_Check) refers to optical.o(i.Refresh_Own_Data) for Refresh_Own_Data
    optical.o(i.Optical_Comm_Check) refers to optical.o(i.Unique_Scr_Check) for Unique_Scr_Check
    optical.o(i.Optical_Comm_Check) refers to optical.o(i.Online_Check) for Online_Check
    optical.o(i.Optical_Comm_Check) refers to optical.o(i.Standby_Check) for Standby_Check
    optical.o(i.Optical_Error_Reset) refers to pub.o(.data) for uchNodeNum
    optical.o(i.Optical_Error_Reset) refers to optical.o(.data) for Optical_Comm_Err_Flag
    optical.o(i.Optical_Error_Reset) refers to optical.o(.bss) for nChNum
    optical.o(i.Pulse_Off_Check) refers to optical.o(.data) for Online_Source
    optical.o(i.Pulse_Off_Check) refers to pub.o(.data) for Pulse_Enabled_Flag
    optical.o(i.Pulse_Test) refers to pub.o(.data) for Pulse_Enabled_Flag
    optical.o(i.Refresh_Alpha_Optical) refers to optical.o(i.VerifySynSource) for VerifySynSource
    optical.o(i.Refresh_Alpha_Optical) refers to optical.o(i.SelectSynSource) for SelectSynSource
    optical.o(i.Refresh_Alpha_Optical) refers to syn.o(i.SelectSyn) for SelectSyn
    optical.o(i.Refresh_Alpha_Optical) refers to optical.o(.bss) for Optical_Scr_Ifd
    optical.o(i.Refresh_Alpha_Optical) refers to pub.o(.data) for nScrNum
    optical.o(i.Refresh_Alpha_Optical) refers to optical.o(.data) for Online_Source
    optical.o(i.Refresh_Own_Data) refers to pub.o(.data) for uchNodeNum
    optical.o(i.Refresh_Own_Data) refers to optical.o(.bss) for Optical_Comm_Status
    optical.o(i.Refresh_Own_Data) refers to optical.o(.data) for Online_Source
    optical.o(i.SCR_Data_Optical) refers to optical.o(i.Float_To_Byte) for Float_To_Byte
    optical.o(i.SCR_Data_Optical) refers to optical.o(.bss) for OpticalTxData
    optical.o(i.SCR_Data_Optical) refers to pub.o(.data) for Online_Flag
    optical.o(i.SCR_Data_Optical) refers to optical.o(.data) for Result_Flag
    optical.o(i.SCR_Data_Optical) refers to pub.o(.bss) for Iarm
    optical.o(i.SCR_Data_Optical) refers to optical.o(i.CRC16) for CRC16
    optical.o(i.Scr_Data_Optical) refers to optical.o(i.Byte_To_Float) for Byte_To_Float
    optical.o(i.Scr_Data_Optical) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    optical.o(i.Scr_Data_Optical) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    optical.o(i.Scr_Data_Optical) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    optical.o(i.Scr_Data_Optical) refers to pub.o(.data) for uchNodeNum
    optical.o(i.Scr_Data_Optical) refers to optical.o(.bss) for Optical_Comm_Status
    optical.o(i.Scr_Data_Optical) refers to optical.o(.data) for Optical_Pulse_Flag
    optical.o(i.Search_Command) refers to optical.o(i.CRC16) for CRC16
    optical.o(i.Search_Command) refers to optical.o(i.Ex_Data_Sensor_ID) for Ex_Data_Sensor_ID
    optical.o(i.Search_Command) refers to optical.o(i.Ex_Data_Optical) for Ex_Data_Optical
    optical.o(i.Search_Command) refers to optical.o(i.Scr_Data_Optical) for Scr_Data_Optical
    optical.o(i.Search_Command) refers to optical.o(.bss) for OpticalRxCount
    optical.o(i.Search_Command) refers to optical.o(.data) for Optical_Rx_Flag
    optical.o(i.Search_Command) refers to pub.o(.bss) for t_Op
    optical.o(i.SelectSynSource) refers to optical.o(.data) for ScrSynError
    optical.o(i.SelectSynSource) refers to pub.o(.data) for Scr_Pulse_Flag
    optical.o(i.Standby_Check) refers to optical.o(.data) for Optical_Standby_Flag
    optical.o(i.Standby_Check) refers to optical.o(.bss) for nStandbyNo
    optical.o(i.Standby_Check) refers to pub.o(.data) for nScrNum
    optical.o(i.Unique_Scr_Check) refers to optical.o(.bss) for Optical_Comm_Status
    optical.o(i.Unique_Scr_Check) refers to optical.o(.data) for Online_Source
    optical.o(i.VerifySynSource) refers to optical.o(.data) for ScrSynError
    optical.o(i.VerifySynSource) refers to pub.o(.data) for Scr_Pulse_Flag
    para.o(i.DIP_Switch_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    para.o(i.DIP_Switch_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    para.o(i.DIP_Switch_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    para.o(i.Init_Coef) refers to para.o(i.Read_ROM) for Read_ROM
    para.o(i.Init_Coef) refers to pub.o(.data) for Avg_Coef
    para.o(i.Init_Coef) refers to pub.o(.bss) for AI_Offset
    para.o(i.Init_DIDO) refers to pub.o(.data) for DO_Lock_Flag
    para.o(i.Init_DIDO) refers to pub.o(.bss) for Tarm
    para.o(i.Init_Hardware) refers to net.o(i.Init_Net) for Init_Net
    para.o(i.Read_DIP_Switch) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    para.o(i.Read_ROM) refers to w25qxx.o(i.W25QXX_Read) for W25QXX_Read
    para.o(i.Read_ROM) refers to optical.o(i.CRC16) for CRC16
    para.o(i.Read_ROM) refers to optical.o(i.Byte_To_Float) for Byte_To_Float
    para.o(i.Read_ROM) refers to para.o(i.charToInt) for charToInt
    para.o(i.Read_ROM) refers to pub.o(.bss) for EE_Data
    para.o(i.Read_ROM) refers to pub.o(.data) for PID_K
    para.o(i.Write_ROM) refers to optical.o(i.Float_To_Byte) for Float_To_Byte
    para.o(i.Write_ROM) refers to para.o(i.intToChar) for intToChar
    para.o(i.Write_ROM) refers to optical.o(i.CRC16) for CRC16
    para.o(i.Write_ROM) refers to w25qxx.o(i.W25QXX_Write) for W25QXX_Write
    para.o(i.Write_ROM) refers to pub.o(.bss) for PID_T
    para.o(i.Write_ROM) refers to pub.o(.data) for PID_K
    ref.o(i.Ref_Com) refers to ref.o(i.Ref_Step_Down) for Ref_Step_Down
    ref.o(i.Ref_Com) refers to ref.o(i.Ref_Step_Up) for Ref_Step_Up
    ref.o(i.Ref_Com) refers to ref.o(i.V_Ref_Q) for V_Ref_Q
    ref.o(i.Ref_Com) refers to ref.o(i.Ref_Tracking) for Ref_Tracking
    ref.o(i.Ref_Com) refers to ref.o(i.Stop_Machine_Check) for Stop_Machine_Check
    ref.o(i.Ref_Step_Down) refers to pub.o(.data) for Ref_Down_Flag
    ref.o(i.Ref_Step_Up) refers to pub.o(.data) for Ref_Up_Flag
    ref.o(i.Ref_Tracking) refers to pub.o(.data) for Online_Flag
    ref.o(i.Stop_Machine_Check) refers to pub.o(.data) for Stop_Command
    ref.o(i.V_Ref_Q) refers to pub.o(.data) for AVR_Flag
    syn.o(i.Cal_Period) refers to pub.o(.data) for Syn_F_Delay
    syn.o(i.Cal_Period) refers to pub.o(.bss) for Period_Source
    syn.o(i.Check_Syn_Error) refers to pub.o(.data) for Syn_A_Err_Flag
    syn.o(i.Clear_Syn_Error) refers to pub.o(.data) for Sec_A_Count
    syn.o(i.InitSynSelect) refers to pub.o(.data) for uchNodeNum
    syn.o(i.Phase_Difference) refers to pub.o(.data) for Period
    syn.o(i.Primary_Syn_Check) refers to syn.o(i.Primary_Syn_Switch_Check) for Primary_Syn_Switch_Check
    syn.o(i.Primary_Syn_Check) refers to pub.o(.data) for Sec_A_Flag
    syn.o(i.Primary_Syn_Switch_Check) refers to optical.o(i.SelectSynSource) for SelectSynSource
    syn.o(i.Primary_Syn_Switch_Check) refers to syn.o(i.SelectSyn) for SelectSyn
    syn.o(i.Primary_Syn_Switch_Check) refers to pub.o(.data) for SynSource
    syn.o(i.Primary_Syn_Switch_Check) refers to optical.o(.data) for ScrSynError
    syn.o(i.Pulse_Syn_Error_Check) refers to optical.o(i.SelectSynSource) for SelectSynSource
    syn.o(i.Pulse_Syn_Error_Check) refers to syn.o(i.SelectSyn) for SelectSyn
    syn.o(i.Pulse_Syn_Error_Check) refers to pub.o(.data) for SynSource
    syn.o(i.Pulse_Syn_Error_Check) refers to optical.o(.data) for ScrSynError
    syn.o(i.Pulse_Syn_Low_F_Check) refers to optical.o(i.SelectSynSource) for SelectSynSource
    syn.o(i.Pulse_Syn_Low_F_Check) refers to syn.o(i.SelectSyn) for SelectSyn
    syn.o(i.Pulse_Syn_Low_F_Check) refers to pub.o(.data) for Pulse_Low_F_Count
    syn.o(i.Pulse_Syn_Low_F_Check) refers to pub.o(.bss) for Scr_Pulse_Count
    syn.o(i.Pulse_Syn_Low_F_Check) refers to optical.o(.data) for ScrSynError
    syn.o(i.Sec_Syn_Check) refers to pub.o(.data) for Sec_A_Flag
    syn.o(i.SelectSyn) refers to pub.o(.data) for uchNodeNum
    syn.o(i.Syn_Check) refers to syn.o(i.Clear_Syn_Error) for Clear_Syn_Error
    syn.o(i.Syn_Check) refers to syn.o(i.Sec_Syn_Check) for Sec_Syn_Check
    syn.o(i.Syn_Check) refers to syn.o(i.Primary_Syn_Check) for Primary_Syn_Check
    syn.o(i.Syn_Check) refers to syn.o(i.Check_Syn_Error) for Check_Syn_Error
    syn.o(i.Syn_Check) refers to pub.o(.data) for Pulse_Enabled_Flag
    syn.o(i.Syn_Pulse_Init) refers to syn.o(i.Clear_Syn_Error) for Clear_Syn_Error
    syn.o(i.Syn_Pulse_Init) refers to syn.o(i.InitSynSelect) for InitSynSelect
    syn.o(i.Syn_Pulse_Init) refers to pub.o(.data) for Syn_A_Work_Flag
    syn.o(i.Syn_Pulse_Init) refers to pub.o(.bss) for Period_Source
    temperature.o(i.Get_T) refers to usartx.o(i.RS485_Rx) for RS485_Rx
    temperature.o(i.Get_T) refers to temperature.o(i.ID_To_No) for ID_To_No
    temperature.o(i.Get_T) refers to temperature.o(i.Data_To_T) for Data_To_T
    temperature.o(i.Get_T) refers to temperature.o(i.T_Filter) for T_Filter
    temperature.o(i.Get_T) refers to temperature.o(.bss) for uchData
    temperature.o(i.Get_T) refers to pub.o(.bss) for T_c
    temperature.o(i.Get_T) refers to temperature.o(.data) for Init_T_Flag
    temperature.o(i.ID_To_No) refers to pub.o(.bss) for Sensor_ID
    temperature.o(i.Init_T) refers to usartx.o(i.Init_RS485) for Init_RS485
    temperature.o(i.Init_T) refers to temperature.o(.bss) for uchT
    temperature.o(i.Init_T) refers to pub.o(.bss) for Tarm
    temperature.o(i.Init_T) refers to temperature.o(.data) for Init_T_Flag
    temperature.o(i.Requst_T) refers to temperature.o(i.Get_T) for Get_T
    tim.o(i.First_Rising_Edge) refers to gd32f4xx_timer.o(i.timer_flag_clear) for timer_flag_clear
    tim.o(i.First_Rising_Edge) refers to gd32f4xx_timer.o(i.timer_disable) for timer_disable
    tim.o(i.First_Rising_Edge) refers to gd32f4xx_timer.o(i.timer_interrupt_disable) for timer_interrupt_disable
    tim.o(i.First_Rising_Edge) refers to tim.o(i.Generate_Pulse) for Generate_Pulse
    tim.o(i.First_Rising_Edge) refers to gd32f4xx_timer.o(i.timer_interrupt_enable) for timer_interrupt_enable
    tim.o(i.First_Rising_Edge) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    tim.o(i.First_Rising_Edge) refers to pub.o(.data) for Pulse_Pointer
    tim.o(i.First_Rising_Edge) refers to tim.o(.bss) for Rising_Edge_Time
    tim.o(i.First_Rising_Edge) refers to tim.o(.data) for Pulse_Data
    tim.o(i.Generate_Tx_Flag) refers to optical.o(.data) for Comm_Count
    tim.o(i.Generate_Tx_Flag) refers to pub.o(.data) for Sec_A_Flag
    tim.o(i.Init_Pulse_Pin) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    tim.o(i.Init_Pulse_Pin) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    tim.o(i.Init_Pulse_Pin) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    tim.o(i.Init_Pulse_Pin) refers to tim.o(i.Generate_Pulse) for Generate_Pulse
    tim.o(i.Init_Pulse_Pin) refers to tim.o(.bss) for Rising_Edge_Time
    tim.o(i.Set_Alpha_Delay) refers to gd32f4xx_timer.o(i.timer_disable) for timer_disable
    tim.o(i.Set_Alpha_Delay) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    tim.o(i.Set_Alpha_Delay) refers to gd32f4xx_timer.o(i.timer_interrupt_enable) for timer_interrupt_enable
    tim.o(i.Set_Alpha_Delay) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    tim.o(i.Set_Alpha_Delay) refers to pub.o(.data) for Alpha
    tim.o(i.TIM1_Init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    tim.o(i.TIM1_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    tim.o(i.TIM1_Init) refers to gd32f4xx_timer.o(i.timer_struct_para_init) for timer_struct_para_init
    tim.o(i.TIM1_Init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    tim.o(i.TIM1_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    tim.o(i.TIM1_Init) refers to gd32f4xx_timer.o(i.timer_flag_clear) for timer_flag_clear
    tim.o(i.TIM1_Init) refers to gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable) for timer_auto_reload_shadow_disable
    tim.o(i.TIM1_Init) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    tim.o(i.TIM2_Init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    tim.o(i.TIM2_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    tim.o(i.TIM2_Init) refers to gd32f4xx_timer.o(i.timer_internal_clock_config) for timer_internal_clock_config
    tim.o(i.TIM2_Init) refers to gd32f4xx_timer.o(i.timer_struct_para_init) for timer_struct_para_init
    tim.o(i.TIM2_Init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    tim.o(i.TIM2_Init) refers to gd32f4xx_timer.o(i.timer_flag_clear) for timer_flag_clear
    tim.o(i.TIM2_Init) refers to gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable) for timer_auto_reload_shadow_disable
    tim.o(i.TIM2_Init) refers to gd32f4xx_timer.o(i.timer_interrupt_enable) for timer_interrupt_enable
    tim.o(i.TIM2_Init) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    tim.o(i.TIM2_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    tim.o(i.TIM3_Init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    tim.o(i.TIM3_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    tim.o(i.TIM3_Init) refers to gd32f4xx_timer.o(i.timer_struct_para_init) for timer_struct_para_init
    tim.o(i.TIM3_Init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    tim.o(i.TIM3_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    tim.o(i.TIM3_Init) refers to gd32f4xx_timer.o(i.timer_flag_clear) for timer_flag_clear
    tim.o(i.TIM3_Init) refers to gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable) for timer_auto_reload_shadow_disable
    tim.o(i.TIM3_Init) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    tim.o(i.TIM3_Update) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    tim.o(i.TIM3_Update) refers to tim.o(i.Generate_Pulse) for Generate_Pulse
    tim.o(i.TIM3_Update) refers to pub.o(.data) for Pulse_Pointer
    tim.o(i.TIM3_Update) refers to tim.o(.data) for Pulse_Data
    tim.o(i.TIM3_Update) refers to tim.o(.bss) for Rising_Edge_Time
    tim.o(i.TIM4_CC1) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    tim.o(i.TIM4_CC1) refers to syn.o(i.Phase_Difference) for Phase_Difference
    tim.o(i.TIM4_CC1) refers to syn.o(i.Cal_Period) for Cal_Period
    tim.o(i.TIM4_CC1) refers to syn.o(i.Syn_Check) for Syn_Check
    tim.o(i.TIM4_CC1) refers to tim.o(i.Generate_Tx_Flag) for Generate_Tx_Flag
    tim.o(i.TIM4_CC1) refers to pub.o(.data) for Syn_A_Int_Time
    tim.o(i.TIM4_CC1) refers to tim.o(.data) for Syn_A_Old_t
    tim.o(i.TIM4_CC2) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    tim.o(i.TIM4_CC2) refers to syn.o(i.Phase_Difference) for Phase_Difference
    tim.o(i.TIM4_CC2) refers to syn.o(i.Cal_Period) for Cal_Period
    tim.o(i.TIM4_CC2) refers to syn.o(i.Syn_Check) for Syn_Check
    tim.o(i.TIM4_CC2) refers to tim.o(i.Generate_Tx_Flag) for Generate_Tx_Flag
    tim.o(i.TIM4_CC2) refers to pub.o(.data) for Syn_B_Int_Time
    tim.o(i.TIM4_CC2) refers to tim.o(.data) for Syn_B_Old_t
    tim.o(i.TIM4_CC4) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    tim.o(i.TIM4_CC4) refers to syn.o(i.Phase_Difference) for Phase_Difference
    tim.o(i.TIM4_CC4) refers to syn.o(i.Cal_Period) for Cal_Period
    tim.o(i.TIM4_CC4) refers to syn.o(i.Syn_Check) for Syn_Check
    tim.o(i.TIM4_CC4) refers to tim.o(i.Generate_Tx_Flag) for Generate_Tx_Flag
    tim.o(i.TIM4_CC4) refers to pub.o(.data) for Syn_C_Int_Time
    tim.o(i.TIM4_CC4) refers to tim.o(.data) for Syn_C_Old_t
    tim.o(i.TIM4_Init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    tim.o(i.TIM4_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    tim.o(i.TIM4_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    tim.o(i.TIM4_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    tim.o(i.TIM4_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    tim.o(i.TIM4_Init) refers to gd32f4xx_timer.o(i.timer_input_capture_config) for timer_input_capture_config
    tim.o(i.TIM4_Init) refers to gd32f4xx_timer.o(i.timer_channel_output_state_config) for timer_channel_output_state_config
    tim.o(i.TIM4_Init) refers to gd32f4xx_timer.o(i.timer_struct_para_init) for timer_struct_para_init
    tim.o(i.TIM4_Init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    tim.o(i.TIM4_Init) refers to gd32f4xx_timer.o(i.timer_autoreload_value_config) for timer_autoreload_value_config
    tim.o(i.TIM4_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    tim.o(i.TIM4_Init) refers to gd32f4xx_timer.o(i.timer_flag_clear) for timer_flag_clear
    tim.o(i.TIM4_Init) refers to gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable) for timer_auto_reload_shadow_disable
    tim.o(i.TIM4_Init) refers to gd32f4xx_timer.o(i.timer_interrupt_enable) for timer_interrupt_enable
    tim.o(i.TIM4_Init) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    tim.o(i.TIM4_Init) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    tim.o(i.TIM8_Init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    tim.o(i.TIM8_Init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    tim.o(i.TIM8_Init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    tim.o(i.TIM8_Init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    tim.o(i.TIM8_Init) refers to gd32f4xx_timer.o(i.timer_deinit) for timer_deinit
    tim.o(i.TIM8_Init) refers to gd32f4xx_timer.o(i.timer_input_capture_config) for timer_input_capture_config
    tim.o(i.TIM8_Init) refers to gd32f4xx_timer.o(i.timer_channel_output_state_config) for timer_channel_output_state_config
    tim.o(i.TIM8_Init) refers to gd32f4xx_timer.o(i.timer_struct_para_init) for timer_struct_para_init
    tim.o(i.TIM8_Init) refers to gd32f4xx_timer.o(i.timer_init) for timer_init
    tim.o(i.TIM8_Init) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    tim.o(i.TIM8_Init) refers to gd32f4xx_timer.o(i.timer_flag_clear) for timer_flag_clear
    tim.o(i.TIM8_Init) refers to gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable) for timer_auto_reload_shadow_disable
    tim.o(i.TIM8_Init) refers to gd32f4xx_timer.o(i.timer_interrupt_enable) for timer_interrupt_enable
    tim.o(i.TIM8_Init) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    tim.o(i.TIM8_Init) refers to gd32f4xx_timer.o(i.timer_enable) for timer_enable
    tim.o(i.TIMER0_BRK_TIMER8_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    tim.o(i.TIMER0_BRK_TIMER8_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    tim.o(i.TIMER0_BRK_TIMER8_IRQHandler) refers to syn.o(i.Cal_Period) for Cal_Period
    tim.o(i.TIMER0_BRK_TIMER8_IRQHandler) refers to tim.o(i.Set_Alpha_Delay) for Set_Alpha_Delay
    tim.o(i.TIMER0_BRK_TIMER8_IRQHandler) refers to tim.o(i.Generate_Tx_Flag) for Generate_Tx_Flag
    tim.o(i.TIMER0_BRK_TIMER8_IRQHandler) refers to syn.o(i.Pulse_Syn_Error_Check) for Pulse_Syn_Error_Check
    tim.o(i.TIMER0_BRK_TIMER8_IRQHandler) refers to pub.o(.data) for Sec_A_Flag
    tim.o(i.TIMER1_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    tim.o(i.TIMER1_IRQHandler) refers to tim.o(i.First_Rising_Edge) for First_Rising_Edge
    tim.o(i.TIMER2_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_clear) for timer_interrupt_flag_clear
    tim.o(i.TIMER2_IRQHandler) refers to ai.o(i.AD) for AD
    tim.o(i.TIMER3_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    tim.o(i.TIMER3_IRQHandler) refers to tim.o(i.TIM3_Update) for TIM3_Update
    tim.o(i.TIMER4_IRQHandler) refers to gd32f4xx_timer.o(i.timer_interrupt_flag_get) for timer_interrupt_flag_get
    tim.o(i.TIMER4_IRQHandler) refers to tim.o(i.TIM4_CC1) for TIM4_CC1
    tim.o(i.TIMER4_IRQHandler) refers to tim.o(i.TIM4_CC2) for TIM4_CC2
    tim.o(i.TIMER4_IRQHandler) refers to tim.o(i.TIM4_CC4) for TIM4_CC4
    tim.o(i.TIMx_Init) refers to tim.o(i.Init_Pulse_Pin) for Init_Pulse_Pin
    tim.o(i.TIMx_Init) refers to tim.o(i.TIM4_Init) for TIM4_Init
    tim.o(i.TIMx_Init) refers to tim.o(i.TIM1_Init) for TIM1_Init
    tim.o(i.TIMx_Init) refers to tim.o(i.TIM2_Init) for TIM2_Init
    tim.o(i.TIMx_Init) refers to tim.o(i.TIM3_Init) for TIM3_Init
    tim.o(i.TIMx_Init) refers to tim.o(i.TIM8_Init) for TIM8_Init
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    acos.o(i.__hardfp_acos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    acos.o(i.__hardfp_acos) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    acos.o(i.__hardfp_acos) refers to _rserrno.o(.text) for __set_errno
    acos.o(i.__hardfp_acos) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    acos.o(i.__hardfp_acos) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    acos.o(i.__hardfp_acos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    acos.o(i.__hardfp_acos) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    acos.o(i.__hardfp_acos) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    acos.o(i.__hardfp_acos) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    acos.o(i.__hardfp_acos) refers to sqrt.o(i.sqrt) for sqrt
    acos.o(i.__hardfp_acos) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    acos.o(i.__hardfp_acos) refers to acos.o(.constdata) for .constdata
    acos.o(i.__softfp_acos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    acos.o(i.__softfp_acos) refers to acos.o(i.__hardfp_acos) for __hardfp_acos
    acos.o(i.acos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    acos.o(i.acos) refers to acos.o(i.__hardfp_acos) for __hardfp_acos
    acos.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    acos_x.o(i.____hardfp_acos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    acos_x.o(i.____hardfp_acos$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    acos_x.o(i.____hardfp_acos$lsc) refers to _rserrno.o(.text) for __set_errno
    acos_x.o(i.____hardfp_acos$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    acos_x.o(i.____hardfp_acos$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    acos_x.o(i.____hardfp_acos$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    acos_x.o(i.____hardfp_acos$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    acos_x.o(i.____hardfp_acos$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    acos_x.o(i.____hardfp_acos$lsc) refers to sqrt.o(i.sqrt) for sqrt
    acos_x.o(i.____hardfp_acos$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    acos_x.o(i.____hardfp_acos$lsc) refers to acos_x.o(.constdata) for .constdata
    acos_x.o(i.____softfp_acos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    acos_x.o(i.____softfp_acos$lsc) refers to acos_x.o(i.____hardfp_acos$lsc) for ____hardfp_acos$lsc
    acos_x.o(i.__acos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    acos_x.o(i.__acos$lsc) refers to acos_x.o(i.____hardfp_acos$lsc) for ____hardfp_acos$lsc
    acos_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.__hardfp_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.__hardfp_cos) refers to _rserrno.o(.text) for __set_errno
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos.o(i.__hardfp_cos) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos.o(i.__hardfp_cos) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos.o(i.__hardfp_cos) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos.o(i.__hardfp_cos) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    cos.o(i.__softfp_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.__softfp_cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos.o(i.cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos.o(i.cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos_x.o(i.____hardfp_cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.____hardfp_cos$lsc) refers to _rserrno.o(.text) for __set_errno
    cos_x.o(i.____hardfp_cos$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos_x.o(i.____hardfp_cos$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos_x.o(i.____hardfp_cos$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos_x.o(i.____hardfp_cos$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos_x.o(i.____hardfp_cos$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    cos_x.o(i.____softfp_cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.____softfp_cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    cos_x.o(i.__cos$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_x.o(i.__cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    pow.o(i.__hardfp_pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to _rserrno.o(.text) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.____hardfp_pow$lsc) refers to _rserrno.o(.text) for __set_errno
    pow_x.o(i.____hardfp_pow$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    pow_x.o(i.____hardfp_pow$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow_x.o(i.____hardfp_pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.____hardfp_pow$lsc) refers to fabs.o(i.fabs) for fabs
    pow_x.o(i.____hardfp_pow$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow_x.o(i.____hardfp_pow$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow_x.o(i.____hardfp_pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow_x.o(i.____hardfp_pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.____hardfp_pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.____hardfp_pow$lsc) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow_x.o(i.____hardfp_pow$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    basic.o(x$fpl$basic) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    cos_i.o(i.__kernel_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    rred.o(i.__ieee754_rem_pio2) refers to fabs.o(i.fabs) for fabs
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    rred.o(i.__ieee754_rem_pio2) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i_x.o(i.____kernel_sin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i_x.o(i.____kernel_sin$lsc) refers to sin_i_x.o(.constdata) for .constdata
    sin_i_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_gd32f450.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing gd32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_adc.o(i.adc_calibration_enable), (42 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_16_to_18), (96 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_length_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_clock_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_data_alignment_config), (22 bytes).
    Removing gd32f4xx_adc.o(i.adc_deinit), (20 bytes).
    Removing gd32f4xx_adc.o(i.adc_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_discontinuous_mode_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_enable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_enable), (18 bytes).
    Removing gd32f4xx_adc.o(i.adc_end_of_conversion_config), (34 bytes).
    Removing gd32f4xx_adc.o(i.adc_external_trigger_config), (52 bytes).
    Removing gd32f4xx_adc.o(i.adc_external_trigger_source_config), (48 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_get), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_config), (124 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_offset_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_data_read), (46 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_disable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_enable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_config), (58 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_disable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_enable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_regular_channel_config), (172 bytes).
    Removing gd32f4xx_adc.o(i.adc_regular_data_read), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_regular_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_resolution_config), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_software_trigger_enable), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_special_function_config), (90 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_delay_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_disable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_mode_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_regular_data_read), (12 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_disable), (50 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_group_channel_enable), (64 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_threshold_config), (14 bytes).
    Removing gd32f4xx_can.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_can.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_can.o(i.can1_filter_start_bank), (56 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_disable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_enable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_deinit), (52 bytes).
    Removing gd32f4xx_can.o(i.can_error_get), (12 bytes).
    Removing gd32f4xx_can.o(i.can_fifo_release), (32 bytes).
    Removing gd32f4xx_can.o(i.can_filter_init), (272 bytes).
    Removing gd32f4xx_can.o(i.can_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_flag_get), (30 bytes).
    Removing gd32f4xx_can.o(i.can_init), (290 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_disable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_enable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_get), (116 bytes).
    Removing gd32f4xx_can.o(i.can_message_receive), (228 bytes).
    Removing gd32f4xx_can.o(i.can_message_transmit), (336 bytes).
    Removing gd32f4xx_can.o(i.can_receive_error_number_get), (8 bytes).
    Removing gd32f4xx_can.o(i.can_receive_message_length_get), (26 bytes).
    Removing gd32f4xx_can.o(i.can_struct_para_init), (164 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_disable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_enable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_transmission_stop), (80 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_error_number_get), (10 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_states), (124 bytes).
    Removing gd32f4xx_can.o(i.can_wakeup), (48 bytes).
    Removing gd32f4xx_can.o(i.can_working_mode_set), (168 bytes).
    Removing gd32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_crc.o(i.crc_block_data_calculate), (36 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_reset), (20 bytes).
    Removing gd32f4xx_crc.o(i.crc_deinit), (24 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_write), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_single_data_calculate), (16 bytes).
    Removing gd32f4xx_ctc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_clock_limit_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_capture_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_direction_read), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_disable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_enable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_deinit), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_get), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_hardware_trim_mode_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_get), (56 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_polarity_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_prescaler_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_signal_select), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_software_refsource_pulse_generate), (20 bytes).
    Removing gd32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_data_set), (64 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_disable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_enable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_interrupt_disable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_interrupt_enable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_disable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_enable), (24 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_disable), (20 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_enable), (20 bytes).
    Removing gd32f4xx_dac.o(i.dac_data_set), (88 bytes).
    Removing gd32f4xx_dac.o(i.dac_deinit), (20 bytes).
    Removing gd32f4xx_dac.o(i.dac_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_clear), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_get), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_get), (68 bytes).
    Removing gd32f4xx_dac.o(i.dac_lfsr_noise_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_value_get), (28 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_triangle_noise_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_disable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_enable), (36 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_source_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_wave_bit_width_config), (52 bytes).
    Removing gd32f4xx_dac.o(i.dac_wave_mode_config), (52 bytes).
    Removing gd32f4xx_dbg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_deinit), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_id_get), (12 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_disable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_enable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_disable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_enable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_disable), (20 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_enable), (20 bytes).
    Removing gd32f4xx_dci.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dci.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_data_read), (12 bytes).
    Removing gd32f4xx_dci.o(i.dci_deinit), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_flag_get), (36 bytes).
    Removing gd32f4xx_dci.o(i.dci_init), (52 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_disable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_enable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_unmask_config), (24 bytes).
    Removing gd32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_disable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_circulation_enable), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_fifo_status_get), (20 bytes).
    Removing gd32f4xx_dma.o(i.dma_flow_controller_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_disable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_enable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_clear), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_get), (516 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_config), (32 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_generation_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_mode_init), (356 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_para_struct_init), (40 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_address_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_peripheral_address_generation_config), (126 bytes).
    Removing gd32f4xx_dma.o(i.dma_priority_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_config), (76 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_enable), (66 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_direction_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_number_get), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_using_memory_get), (28 bytes).
    Removing gd32f4xx_enet.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_enet.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_config), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_current_desc_address_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_debug_status_get), (108 bytes).
    Removing gd32f4xx_enet.o(i.enet_default_init), (104 bytes).
    Removing gd32f4xx_enet.o(i.enet_deinit), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_delay), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_clear), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_get), (14 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_set), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_information_get), (100 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_select_normal_mode), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_chain_init), (200 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_ring_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_disable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_resume), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_state_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_enable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_threshold_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_disable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_receive), (248 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_transmit), (204 bytes).
    Removing gd32f4xx_enet.o(i.enet_init), (868 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_config), (356 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_reset), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_disable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_enable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_get), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_set), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_missed_frame_counter_get), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_get), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_preset_config), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_reset), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_config), (44 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_detect_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_generate), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_config), (216 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_write_read), (156 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_disable), (50 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_expected_time_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init), (280 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_pps_output_frequency_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_subsecond_increment_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_system_time_get), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_addend_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_function_config), (256 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_update_config), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode), (340 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode), (444 bytes).
    Removing gd32f4xx_enet.o(i.enet_registers_get), (56 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_delay_receive_complete_interrupt), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_immediate_receive_complete_interrupt), (10 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_disable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_enable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_drop), (172 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_size_get), (152 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxprocess_check_recovery), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_software_reset), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_transmit_checksum_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_txfifo_flush), (52 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_register_pointer_reset), (20 bytes).
    Removing gd32f4xx_enet.o(.bss), (15460 bytes).
    Removing gd32f4xx_enet.o(.constdata), (116 bytes).
    Removing gd32f4xx_enet.o(.data), (20 bytes).
    Removing gd32f4xx_exmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_ecc_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_clear), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_get), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_disable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_enable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_clear), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_get), (72 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_deinit), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_ecc_config), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_init), (172 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_struct_para_init), (54 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_consecutive_clock_config), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_init), (228 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_page_size_config), (40 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_struct_para_init), (106 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_disable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_enable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_init), (188 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_struct_para_init), (60 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_autorefresh_number_set), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_bankstatus_get), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_command_config), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_deinit), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_init), (284 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_config), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_enable), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_refresh_count_set), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_command_para_init), (16 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_para_init), (66 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_write_protection_config), (64 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_deinit), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_high_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_init), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_low_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_command_set), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_id_command_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_send_command_state_get), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_struct_para_init), (20 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_cmd_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_command_set), (28 bytes).
    Removing gd32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exti.o(i.exti_deinit), (28 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_init), (188 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank0_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank1_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_byte_program), (80 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_get), (24 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_halfword_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_get), (64 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_mass_erase), (72 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_ready_wait), (32 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_sector_erase), (96 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_state_get), (76 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_word_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_wscnt_set), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_boot_mode_config), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp0_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp1_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_disable), (96 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_enable), (104 bytes).
    Removing gd32f4xx_fmc.o(i.ob_erase), (76 bytes).
    Removing gd32f4xx_fmc.o(i.ob_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_security_protection_config), (40 bytes).
    Removing gd32f4xx_fmc.o(i.ob_spc_get), (28 bytes).
    Removing gd32f4xx_fmc.o(i.ob_start), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_write), (52 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection0_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection1_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_disable), (72 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_enable), (72 bytes).
    Removing gd32f4xx_fwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_config), (104 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_counter_reload), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_enable), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_prescaler_value_config), (60 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_reload_value_config), (64 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_disable), (12 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_enable), (16 bytes).
    Removing gd32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_toggle), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_write), (10 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_deinit), (206 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_input_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_bit_get), (16 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_pin_lock), (18 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_toggle), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_write), (4 bytes).
    Removing gd32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ack_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ackpos_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_clock_config), (228 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_receive), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_transmit), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_deinit), (88 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_digital_noise_filter_config), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_last_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_enable), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_clear), (40 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_flag_get), (30 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_disable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_enable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_clear), (44 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_get), (92 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_master_addressing), (20 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_mode_addr_config), (28 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_value_get), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_slave_response_to_gcall_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_alert_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_arp_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_type_config), (24 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_software_reset_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_start_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stop_on_bus), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stretch_scl_low_config), (16 bytes).
    Removing gd32f4xx_ipa.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_deinit), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_init), (316 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_struct_para_init), (22 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_inter_timer_config), (36 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interval_clock_num_config), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_line_mark_config), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_pixel_format_convert_mode_set), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_enable), (20 bytes).
    Removing gd32f4xx_iref.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_iref.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_iref.o(i.iref_deinit), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_disable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_enable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_mode_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_precision_trim_value_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_sink_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_step_data_config), (28 bytes).
    Removing gd32f4xx_misc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_misc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_misc.o(i.nvic_irq_disable), (24 bytes).
    Removing gd32f4xx_misc.o(i.nvic_irq_enable), (196 bytes).
    Removing gd32f4xx_misc.o(i.nvic_vector_table_set), (24 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_reset), (16 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_set), (16 bytes).
    Removing gd32f4xx_misc.o(i.systick_clksource_set), (40 bytes).
    Removing gd32f4xx_pmu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_ldo_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_write_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_write_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_deinit), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_clear), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_get), (24 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_switch_select), (44 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_ldo_output_select), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_select), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_normalpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_deepsleepmode), (244 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_sleepmode), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_standbymode), (100 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(.bss), (16 bytes).
    Removing gd32f4xx_rcu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ahb_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb1_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb2_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ck48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout0_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout1_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_clock_freq_get), (292 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deepsleep_voltage_set), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deinit), (140 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_flag_get), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_i2s_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_irc16m_adjust_value_set), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_lxtal_drive_capability_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_disable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_enable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_off), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_on), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_stab_wait), (348 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll_config), (132 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_plli2s_config), (44 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pllsai_config), (72 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_rtc_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_rtc_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_config), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_get), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_tli_clock_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_voltage_key_unlock), (16 bytes).
    Removing gd32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_config), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_disable), (128 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_get), (68 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_output_config), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_config), (52 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_get), (32 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_calibration_output_config), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_config), (116 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_current_time_get), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_deinit), (204 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_clear), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_hour_adjust), (36 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_init), (196 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_init_mode_enter), (72 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_init_mode_exit), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_disable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_register_sync_wait), (96 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_second_adjust), (108 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_smooth_calibration_config), (80 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_subsecond_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper0_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_disable), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_enable), (200 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_enable), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_get), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_clock_set), (92 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_disable), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_set), (76 bytes).
    Removing gd32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_bus_mode_set), (28 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_config), (52 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_command_index_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_command_response_config), (56 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_config), (60 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_read), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_transfer_config), (28 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_write), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_deinit), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dma_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dma_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dsm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dsm_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_fifo_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_flag_clear), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_flag_get), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_disable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_enable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_flag_get), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_power_state_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_power_state_set), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_type_set), (40 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_response_get), (60 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_wait_type_set), (28 bytes).
    Removing gd32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_spi.o(i.i2s_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_full_duplex_mode_config), (48 bytes).
    Removing gd32f4xx_spi.o(i.i2s_init), (28 bytes).
    Removing gd32f4xx_spi.o(i.i2s_psc_config), (292 bytes).
    Removing gd32f4xx_spi.o(i.spi_bidirectional_transfer_config), (26 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_error_clear), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_get), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_next), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_off), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_on), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_get), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_frame_format_config), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_disable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_enable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_high), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_low), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_io23_output_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_io23_output_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_read_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_write_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_struct_para_init), (18 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_enable), (10 bytes).
    Removing gd32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_bootmode_config), (28 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_compensation_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_deinit), (20 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_enet_phy_interface_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exmc_swap_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exti_line_config), (172 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_flag_get), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_fmc_swap_config), (24 bytes).
    Removing gd32f4xx_timer.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_timer.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_autoreload_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_struct_para_init), (18 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_capture_value_register_read), (42 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_polarity_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_state_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_update_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_dma_request_source_select), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_clear_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_config), (492 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_fast_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_mode_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_polarity_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config), (38 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_shadow_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_state_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_remap_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_alignment), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_down_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_read), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_up_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_deinit), (388 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_transfer_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_event_software_generate), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode0_config), (40 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config), (166 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_get), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_hall_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_init), (152 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_capture_config), (326 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_pwm_capture_config), (356 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_clock_config), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_output_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_slave_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_output_value_selection_config), (34 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_config), (14 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_read), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_primary_output_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_quadrature_decoder_mode_config), (64 bytes).
    Removing gd32f4xx_timer.o(i.timer_repetition_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_single_pulse_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_slave_mode_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_struct_para_init), (22 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_source_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_write_chxval_register_config), (34 bytes).
    Removing gd32f4xx_tli.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_tli.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_init), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_current_pos_get), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_deinit), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_disable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_dither_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_enable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_init), (188 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_disable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_enable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_init), (152 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_struct_para_init), (48 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_window_offset_modify), (228 bytes).
    Removing gd32f4xx_tli.o(i.tli_line_mark_set), (24 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_init), (28 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_struct_para_init), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_reload_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_struct_para_init), (34 bytes).
    Removing gd32f4xx_trng.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_trng.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_trng.o(i.trng_deinit), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_flag_get), (24 bytes).
    Removing gd32f4xx_trng.o(i.trng_get_true_random_data), (12 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_usart.o(i.usart_address_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_baudrate_set), (232 bytes).
    Removing gd32f4xx_usart.o(i.usart_block_length_config), (28 bytes).
    Removing gd32f4xx_usart.o(i.usart_break_frame_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_data_first_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_data_receive), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_data_transmit), (8 bytes).
    Removing gd32f4xx_usart.o(i.usart_deinit), (220 bytes).
    Removing gd32f4xx_usart.o(i.usart_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_receive_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_transmit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_flag_clear), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_flag_get), (30 bytes).
    Removing gd32f4xx_usart.o(i.usart_guard_time_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_cts_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_rts_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_disable), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_enable), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_flag_clear), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_interrupt_flag_get), (56 bytes).
    Removing gd32f4xx_usart.o(i.usart_invert_config), (104 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_lowpower_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_break_detection_length_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_wakeup_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_oversample_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_check_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_prescaler_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_receive_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_disable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_enable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_threshold_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_sample_bit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_send_break), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_autoretry_config), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_stop_bit_set), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_config), (34 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_transmit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_word_length_set), (16 bytes).
    Removing gd32f4xx_wwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_config), (28 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_counter_update), (16 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_deinit), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_enable), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_clear), (12 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_interrupt_enable), (20 bytes).
    Removing system_gd32f4xx.o(.rev16_text), (4 bytes).
    Removing system_gd32f4xx.o(.revsh_text), (4 bytes).
    Removing system_gd32f4xx.o(i.SystemCoreClockUpdate), (272 bytes).
    Removing system_gd32f4xx.o(.data), (4 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(i.DI16_Input), (46 bytes).
    Removing gpio.o(i.DO16_Out), (52 bytes).
    Removing gpio.o(i.DO_Out), (100 bytes).
    Removing gpio.o(i.Delayed_DI), (32 bytes).
    Removing gpio.o(i.GPIO), (16 bytes).
    Removing gpio.o(i.GPIO_FSMC_Init), (2 bytes).
    Removing gpio.o(i.Init_DIDO_Para), (104 bytes).
    Removing gpio.o(i.Init_GPIO), (36 bytes).
    Removing gpio.o(i.Refresh_DI), (36 bytes).
    Removing gpio.o(i.Refresh_Delayed_DI), (68 bytes).
    Removing gpio.o(.bss), (448 bytes).
    Removing gpio.o(.data), (3 bytes).
    Removing nandflash_sram.o(.rev16_text), (4 bytes).
    Removing nandflash_sram.o(.revsh_text), (4 bytes).
    Removing nandflash_sram.o(i.SRAM_Init), (1680 bytes).
    Removing nandflash_sram.o(i.SRAM_ReadBuffer), (22 bytes).
    Removing nandflash_sram.o(i.SRAM_WriteBuffer), (24 bytes).
    Removing optical_fiber.o(.rev16_text), (4 bytes).
    Removing optical_fiber.o(.revsh_text), (4 bytes).
    Removing optical_fiber.o(i.BaudRate_Init), (20 bytes).
    Removing optical_fiber.o(i.FPGA_DONE_Init), (32 bytes).
    Removing optical_fiber.o(i.FPGA_INITB_Init), (2 bytes).
    Removing optical_fiber.o(i.FPGA_RST_Init), (32 bytes).
    Removing optical_fiber.o(i.Optical_fiber_FSMC_Init), (1680 bytes).
    Removing optical_fiber.o(i.Optical_fiber_Init), (88 bytes).
    Removing optical_fiber.o(i.Read_State), (12 bytes).
    Removing optical_fiber.o(i.Rx_Buffer), (54 bytes).
    Removing optical_fiber.o(i.Tx_Buffer), (32 bytes).
    Removing watchdog.o(.rev16_text), (4 bytes).
    Removing watchdog.o(.revsh_text), (4 bytes).
    Removing watchdog.o(i.Feed_Dog), (16 bytes).
    Removing watchdog.o(i.WatchDog_Init), (48 bytes).
    Removing usartx.o(.rev16_text), (4 bytes).
    Removing usartx.o(.revsh_text), (4 bytes).
    Removing usartx.o(i.Init_RS232), (32 bytes).
    Removing usartx.o(i.Init_RS485), (32 bytes).
    Removing usartx.o(i.RS232_Init_DMA), (20 bytes).
    Removing usartx.o(i.RS232_Init_DMA_Rx), (104 bytes).
    Removing usartx.o(i.RS232_Init_DMA_Tx), (104 bytes).
    Removing usartx.o(i.RS232_Init_Para), (132 bytes).
    Removing usartx.o(i.RS232_Init_Pin), (112 bytes).
    Removing usartx.o(i.RS232_Init_Port), (104 bytes).
    Removing usartx.o(i.RS232_Rx), (184 bytes).
    Removing usartx.o(i.RS232_Tx), (148 bytes).
    Removing usartx.o(i.RS485_Init_DMA), (20 bytes).
    Removing usartx.o(i.RS485_Init_DMA_Rx), (104 bytes).
    Removing usartx.o(i.RS485_Init_DMA_Tx), (104 bytes).
    Removing usartx.o(i.RS485_Init_Para), (96 bytes).
    Removing usartx.o(i.RS485_Init_Pin), (92 bytes).
    Removing usartx.o(i.RS485_Init_Port), (104 bytes).
    Removing usartx.o(i.RS485_Rx), (184 bytes).
    Removing usartx.o(i.RS485_Tx), (152 bytes).
    Removing usartx.o(.bss), (2048 bytes).
    Removing usartx.o(.data), (42 bytes).
    Removing w25qxx.o(.rev16_text), (4 bytes).
    Removing w25qxx.o(.revsh_text), (4 bytes).
    Removing w25qxx.o(i.Start_W25QXX_Write), (44 bytes).
    Removing w25qxx.o(i.W25QXX_EraseSector_Cycle), (56 bytes).
    Removing w25qxx.o(i.W25QXX_Init), (228 bytes).
    Removing w25qxx.o(i.W25QXX_Init_Flag), (108 bytes).
    Removing w25qxx.o(i.W25QXX_ReInit), (120 bytes).
    Removing w25qxx.o(i.W25QXX_Read), (84 bytes).
    Removing w25qxx.o(i.W25QXX_ReadId), (72 bytes).
    Removing w25qxx.o(i.W25QXX_ReadSR), (44 bytes).
    Removing w25qxx.o(i.W25QXX_SPI_WriteByte), (76 bytes).
    Removing w25qxx.o(i.W25QXX_Write), (376 bytes).
    Removing w25qxx.o(i.W25QXX_Write_Cycle), (400 bytes).
    Removing w25qxx.o(i.W25QXX_Write_Disable), (32 bytes).
    Removing w25qxx.o(i.W25QXX_Write_Enable), (32 bytes).
    Removing w25qxx.o(i.W25QXX_Write_Test), (60 bytes).
    Removing w25qxx.o(i.W25QXX_Write_Verify), (68 bytes).
    Removing w25qxx.o(i.sFLASH_WritePage_Cycle), (140 bytes).
    Removing w25qxx.o(.bss), (4864 bytes).
    Removing w25qxx.o(.data), (40 bytes).
    Removing cl1606_spi.o(.rev16_text), (4 bytes).
    Removing cl1606_spi.o(.revsh_text), (4 bytes).
    Removing cl1606_spi.o(i.AD7606_Data_Init), (2 bytes).
    Removing cl1606_spi.o(i.AD7606_PIN_Init), (300 bytes).
    Removing cl1606_spi.o(i.AD7606_Read_Reg1), (24 bytes).
    Removing cl1606_spi.o(i.AD7606_Start_Convst1), (44 bytes).
    Removing cl1606_spi.o(i.CL1606_Data_Init), (2 bytes).
    Removing cl1606_spi.o(i.CL1606_PIN_Init), (320 bytes).
    Removing cl1606_spi.o(i.CL1606_RST_Off), (20 bytes).
    Removing cl1606_spi.o(i.Read_AD_Sample1), (32 bytes).
    Removing cl1606_spi.o(i.SCT_AD_Init), (48 bytes).
    Removing cl1606_spi.o(i.SCT_AD_R_Data1), (78 bytes).
    Removing cl1606_spi.o(i.SPIx_RW_One_Byte1), (68 bytes).
    Removing internet.o(.rev16_text), (4 bytes).
    Removing internet.o(.revsh_text), (4 bytes).
    Removing internet.o(i.Network10_Disconnect), (64 bytes).
    Removing internet.o(i.Network1_Bind_Socket), (44 bytes).
    Removing internet.o(i.Network1_Disconnect), (76 bytes).
    Removing internet.o(i.Network1_Get_Set_Remote_IP_Port_Length), (100 bytes).
    Removing internet.o(i.Network1_Phy_Status), (32 bytes).
    Removing internet.o(i.Network1_Set_Remote_IP_Port), (68 bytes).
    Removing internet.o(i.Network1_Setsockopt), (48 bytes).
    Removing internet.o(i.Network20_Disconnect), (64 bytes).
    Removing internet.o(i.Network2_Bind_Socket), (44 bytes).
    Removing internet.o(i.Network2_Disconnect), (76 bytes).
    Removing internet.o(i.Network2_Get_Set_Remote_IP_Port_Length), (100 bytes).
    Removing internet.o(i.Network2_Phy_Status), (32 bytes).
    Removing internet.o(i.Network2_Set_Remote_IP_Port), (68 bytes).
    Removing internet.o(i.Network2_Setsockopt), (48 bytes).
    Removing internet.o(i.Read_SOCK_ByteX_W5500), (218 bytes).
    Removing internet.o(i.Read_W5500), (210 bytes).
    Removing internet.o(i.SocketU1_Connect), (20 bytes).
    Removing internet.o(i.SocketU1_Listen), (20 bytes).
    Removing internet.o(i.SocketU2_Connect), (20 bytes).
    Removing internet.o(i.SocketU2_Listen), (20 bytes).
    Removing internet.o(i.W5500_SOCK_Connect), (76 bytes).
    Removing internet.o(i.W5500_SOCK_Listen), (106 bytes).
    Removing internet.o(.constdata), (14 bytes).
    Removing ai.o(.rev16_text), (4 bytes).
    Removing ai.o(.revsh_text), (4 bytes).
    Removing ai.o(i.AD1), (16 bytes).
    Removing ai.o(i.Cal_AD), (900 bytes).
    Removing ai.o(i.Cal_Arm_I), (500 bytes).
    Removing ai.o(i.Cal_Avg), (248 bytes).
    Removing ai.o(i.Get_Medium_Value), (90 bytes).
    Removing ai.o(i.Get_Monitor), (656 bytes).
    Removing ai.o(i.Init_AD_Data), (636 bytes).
    Removing ai.o(i.Init_Avg), (64 bytes).
    Removing ai.o(i.RMS), (136 bytes).
    Removing ai.o(i.Shift_AD_Data), (52 bytes).
    Removing ai.o(i.Zero_Calibrate), (88 bytes).
    Removing ai.o(.bss), (23056 bytes).
    Removing ai.o(.data), (8 bytes).
    Removing command.o(.rev16_text), (4 bytes).
    Removing command.o(.revsh_text), (4 bytes).
    Removing command.o(i.Init_RS232_Comm), (20 bytes).
    Removing command.o(i.Init_USART_Data), (76 bytes).
    Removing command.o(i.Monitor_Command), (280 bytes).
    Removing command.o(i.RS232_Comm), (116 bytes).
    Removing command.o(i.Status_Data), (804 bytes).
    Removing dido.o(.rev16_text), (4 bytes).
    Removing dido.o(.revsh_text), (4 bytes).
    Removing dido.o(i.Breaker_Check), (148 bytes).
    Removing dido.o(i.DI), (20 bytes).
    Removing dido.o(i.DIDO), (12 bytes).
    Removing dido.o(i.DO), (52 bytes).
    Removing dido.o(i.DO1), (280 bytes).
    Removing dido.o(i.DO2), (184 bytes).
    Removing dido.o(i.DO3), (144 bytes).
    Removing dido.o(i.DO4), (80 bytes).
    Removing dido.o(i.DO5), (48 bytes).
    Removing dido.o(i.DO6), (64 bytes).
    Removing dido.o(i.DO_Lock_Check), (36 bytes).
    Removing dido.o(i.Get_DI1), (128 bytes).
    Removing dido.o(i.Get_DI2), (52 bytes).
    Removing dido.o(i.Low_F_Check), (96 bytes).
    Removing dido.o(i.Low_F_Trigger), (80 bytes).
    Removing dido.o(i.Refresh_DO_Flag), (20 bytes).
    Removing dido.o(i.Running_Signal), (40 bytes).
    Removing dido.o(i.SCR_Check), (108 bytes).
    Removing eifd.o(.rev16_text), (4 bytes).
    Removing eifd.o(.revsh_text), (4 bytes).
    Removing eifd.o(i.AVR), (416 bytes).
    Removing eifd.o(i.Init_PID), (300 bytes).
    Removing eifd.o(i.PID), (432 bytes).
    Removing eifd.o(.data), (14 bytes).
    Removing limit.o(i.Init_Limit), (100 bytes).
    Removing limit.o(i.Limit_Check), (72 bytes).
    Removing limit.o(i.Over_Ex_Check), (596 bytes).
    Removing limit.o(i.PT_Check), (380 bytes).
    Removing limit.o(i.Scr_Exit_Check), (372 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.data), (8 bytes).
    Removing mode.o(i.Work_Mode_Ifd), (260 bytes).
    Removing mode.o(i.Work_Mode_V), (276 bytes).
    Removing mode.o(i.Work_Mode_Vfd), (212 bytes).
    Removing net.o(.rev16_text), (4 bytes).
    Removing net.o(.revsh_text), (4 bytes).
    Removing optical.o(.rev16_text), (4 bytes).
    Removing optical.o(.revsh_text), (4 bytes).
    Removing optical.o(i.CRC16), (56 bytes).
    Removing optical.o(i.Calc_Update_Flag), (80 bytes).
    Removing optical.o(i.Clear_Comm_Err_Module), (168 bytes).
    Removing optical.o(i.Collect_Sensor_ID), (96 bytes).
    Removing optical.o(i.Ex_Data_Optical), (428 bytes).
    Removing optical.o(i.Ex_Data_Sensor_ID), (68 bytes).
    Removing optical.o(i.Float_To_Byte), (40 bytes).
    Removing optical.o(i.Get_Command), (516 bytes).
    Removing optical.o(i.Get_eIfd_Command), (136 bytes).
    Removing optical.o(i.Init_Optical_Comm), (628 bytes).
    Removing optical.o(i.Online_Check), (696 bytes).
    Removing optical.o(i.Optical_Comm), (292 bytes).
    Removing optical.o(i.Optical_Comm_Check), (20 bytes).
    Removing optical.o(i.Optical_Error_Reset), (176 bytes).
    Removing optical.o(i.Pulse_Off_Check), (84 bytes).
    Removing optical.o(i.Pulse_Test), (80 bytes).
    Removing optical.o(i.Refresh_Alpha_Optical), (812 bytes).
    Removing optical.o(i.Refresh_Own_Data), (368 bytes).
    Removing optical.o(i.SCR_Data_Optical), (1432 bytes).
    Removing optical.o(i.Scr_Data_Optical), (444 bytes).
    Removing optical.o(i.Search_Command), (604 bytes).
    Removing optical.o(i.Standby_Check), (356 bytes).
    Removing optical.o(i.Unique_Scr_Check), (140 bytes).
    Removing optical.o(i.VerifySynSource), (48 bytes).
    Removing optical.o(.bss), (3856 bytes).
    Removing para.o(.rev16_text), (4 bytes).
    Removing para.o(.revsh_text), (4 bytes).
    Removing para.o(i.DIP_Switch_Init), (100 bytes).
    Removing para.o(i.Get_Hardware_Version), (2 bytes).
    Removing para.o(i.Init_Coef), (528 bytes).
    Removing para.o(i.Init_DIDO), (428 bytes).
    Removing para.o(i.Init_RTC), (2 bytes).
    Removing para.o(i.Read_DIP_Switch), (60 bytes).
    Removing para.o(i.Read_ROM), (348 bytes).
    Removing para.o(i.Write_ROM), (404 bytes).
    Removing para.o(i.charToInt), (16 bytes).
    Removing para.o(i.intToChar), (10 bytes).
    Removing ref.o(i.Ref_Com), (24 bytes).
    Removing ref.o(i.Ref_Step_Down), (360 bytes).
    Removing ref.o(i.Ref_Step_Up), (384 bytes).
    Removing ref.o(i.Ref_Tracking), (216 bytes).
    Removing ref.o(i.Stop_Machine_Check), (184 bytes).
    Removing ref.o(i.V_Ref_Q), (92 bytes).
    Removing syn.o(.rev16_text), (4 bytes).
    Removing syn.o(.revsh_text), (4 bytes).
    Removing syn.o(i.InitSynSelect), (24 bytes).
    Removing syn.o(i.Pulse_Syn_Low_F_Check), (192 bytes).
    Removing syn.o(i.Syn_Pulse_Init), (340 bytes).
    Removing temperature.o(.rev16_text), (4 bytes).
    Removing temperature.o(.revsh_text), (4 bytes).
    Removing temperature.o(i.Data_To_T), (156 bytes).
    Removing temperature.o(i.Get_T), (348 bytes).
    Removing temperature.o(i.ID_To_No), (52 bytes).
    Removing temperature.o(i.Init_T), (152 bytes).
    Removing temperature.o(i.Requst_T), (8 bytes).
    Removing temperature.o(i.T_Filter), (316 bytes).
    Removing temperature.o(.bss), (728 bytes).
    Removing temperature.o(.data), (12 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(i.Init_Pulse_Pin), (112 bytes).
    Removing tim.o(i.TIM1_Init), (70 bytes).
    Removing tim.o(i.TIM2_Init), (92 bytes).
    Removing tim.o(i.TIM3_Init), (84 bytes).
    Removing tim.o(i.TIM4_Init), (252 bytes).
    Removing tim.o(i.TIM8_Init), (180 bytes).
    Removing tim.o(i.TIMx_Init), (28 bytes).

1121 unused section(s) (total 122595 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/basic.s                         0x00000000   Number         0  basic.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_umaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/acos.c                        0x00000000   Number         0  acos_x.o ABSOLUTE
    ../mathlib/acos.c                        0x00000000   Number         0  acos.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos_x.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos.o ABSOLUTE
    ../mathlib/cos_i.c                       0x00000000   Number         0  cos_i.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i_x.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    source\AI.c                              0x00000000   Number         0  ai.o ABSOLUTE
    source\CL1606_SPI.c                      0x00000000   Number         0  cl1606_spi.o ABSOLUTE
    source\DIDO.c                            0x00000000   Number         0  dido.o ABSOLUTE
    source\GPIO.C                            0x00000000   Number         0  gpio.o ABSOLUTE
    source\TIM.C                             0x00000000   Number         0  tim.o ABSOLUTE
    source\USARTx.C                          0x00000000   Number         0  usartx.o ABSOLUTE
    source\W25QXX.c                          0x00000000   Number         0  w25qxx.o ABSOLUTE
    source\WatchDog.c                        0x00000000   Number         0  watchdog.o ABSOLUTE
    source\\AI.c                             0x00000000   Number         0  ai.o ABSOLUTE
    source\\CL1606_SPI.c                     0x00000000   Number         0  cl1606_spi.o ABSOLUTE
    source\\DIDO.c                           0x00000000   Number         0  dido.o ABSOLUTE
    source\\GPIO.C                           0x00000000   Number         0  gpio.o ABSOLUTE
    source\\TIM.C                            0x00000000   Number         0  tim.o ABSOLUTE
    source\\USARTx.C                         0x00000000   Number         0  usartx.o ABSOLUTE
    source\\W25QXX.c                         0x00000000   Number         0  w25qxx.o ABSOLUTE
    source\\WatchDog.c                       0x00000000   Number         0  watchdog.o ABSOLUTE
    source\\command.c                        0x00000000   Number         0  command.o ABSOLUTE
    source\\eIfd.c                           0x00000000   Number         0  eifd.o ABSOLUTE
    source\\internet.c                       0x00000000   Number         0  internet.o ABSOLUTE
    source\\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    source\\nandflash_sram.c                 0x00000000   Number         0  nandflash_sram.o ABSOLUTE
    source\\net.c                            0x00000000   Number         0  net.o ABSOLUTE
    source\\optical.c                        0x00000000   Number         0  optical.o ABSOLUTE
    source\\optical_fiber.c                  0x00000000   Number         0  optical_fiber.o ABSOLUTE
    source\\para.c                           0x00000000   Number         0  para.o ABSOLUTE
    source\\syn.c                            0x00000000   Number         0  syn.o ABSOLUTE
    source\\temperature.c                    0x00000000   Number         0  temperature.o ABSOLUTE
    source\command.c                         0x00000000   Number         0  command.o ABSOLUTE
    source\eIfd.c                            0x00000000   Number         0  eifd.o ABSOLUTE
    source\internet.c                        0x00000000   Number         0  internet.o ABSOLUTE
    source\limit.c                           0x00000000   Number         0  limit.o ABSOLUTE
    source\main.c                            0x00000000   Number         0  main.o ABSOLUTE
    source\mode.c                            0x00000000   Number         0  mode.o ABSOLUTE
    source\nandflash_sram.c                  0x00000000   Number         0  nandflash_sram.o ABSOLUTE
    source\net.c                             0x00000000   Number         0  net.o ABSOLUTE
    source\optical.c                         0x00000000   Number         0  optical.o ABSOLUTE
    source\optical_fiber.c                   0x00000000   Number         0  optical_fiber.o ABSOLUTE
    source\para.c                            0x00000000   Number         0  para.o ABSOLUTE
    source\pub.c                             0x00000000   Number         0  pub.o ABSOLUTE
    source\ref.c                             0x00000000   Number         0  ref.o ABSOLUTE
    source\syn.c                             0x00000000   Number         0  syn.o ABSOLUTE
    source\temperature.c                     0x00000000   Number         0  temperature.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_adc.c           0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_can.c           0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_crc.c           0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_ctc.c           0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_dac.c           0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_dbg.c           0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_dci.c           0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_dma.c           0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_enet.c          0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_exmc.c          0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_exti.c          0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_fmc.c           0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_fwdgt.c         0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_gpio.c          0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_i2c.c           0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_ipa.c           0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_iref.c          0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_misc.c          0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_pmu.c           0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_rcu.c           0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_rtc.c           0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_sdio.c          0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_spi.c           0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_syscfg.c        0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_timer.c         0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_tli.c           0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_trng.c          0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_usart.c         0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    stdDriver\\src\\gd32f4xx_wwdgt.c         0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    stdDriver\\src\\system_gd32f4xx.c        0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    stdDriver\src\gd32f4xx_adc.c             0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    stdDriver\src\gd32f4xx_can.c             0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    stdDriver\src\gd32f4xx_crc.c             0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    stdDriver\src\gd32f4xx_ctc.c             0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    stdDriver\src\gd32f4xx_dac.c             0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    stdDriver\src\gd32f4xx_dbg.c             0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    stdDriver\src\gd32f4xx_dci.c             0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    stdDriver\src\gd32f4xx_dma.c             0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    stdDriver\src\gd32f4xx_enet.c            0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    stdDriver\src\gd32f4xx_exmc.c            0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    stdDriver\src\gd32f4xx_exti.c            0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    stdDriver\src\gd32f4xx_fmc.c             0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    stdDriver\src\gd32f4xx_fwdgt.c           0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    stdDriver\src\gd32f4xx_gpio.c            0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    stdDriver\src\gd32f4xx_i2c.c             0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    stdDriver\src\gd32f4xx_ipa.c             0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    stdDriver\src\gd32f4xx_iref.c            0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    stdDriver\src\gd32f4xx_misc.c            0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    stdDriver\src\gd32f4xx_pmu.c             0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    stdDriver\src\gd32f4xx_rcu.c             0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    stdDriver\src\gd32f4xx_rtc.c             0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    stdDriver\src\gd32f4xx_sdio.c            0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    stdDriver\src\gd32f4xx_spi.c             0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    stdDriver\src\gd32f4xx_syscfg.c          0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    stdDriver\src\gd32f4xx_timer.c           0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    stdDriver\src\gd32f4xx_tli.c             0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    stdDriver\src\gd32f4xx_trng.c            0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    stdDriver\src\gd32f4xx_usart.c           0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    stdDriver\src\gd32f4xx_wwdgt.c           0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    stdDriver\src\startup_gd32f450.s         0x00000000   Number         0  startup_gd32f450.o ABSOLUTE
    stdDriver\src\system_gd32f4xx.c          0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_gd32f450.o(RESET)
    !!!main                                  0x080001ac   Section        8  __main.o(!!!main)
    !!!scatter                               0x080001b4   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x080001e8   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000244   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x08000260   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x08000262   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000266   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000266   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000268   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0800026a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0800026a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x0800026a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x0800026a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x0800026a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x0800026a   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x0800026a   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x0800026c   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x0800026c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x0800026c   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000272   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000272   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000276   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000276   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800027e   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000280   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000280   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000284   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x0800028c   Section       64  startup_gd32f450.o(.text)
    $v0                                      0x0800028c   Number         0  startup_gd32f450.o(.text)
    .text                                    0x080002cc   Section        0  heapauxi.o(.text)
    .text                                    0x080002d4   Section        8  libspace.o(.text)
    .text                                    0x080002dc   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000326   Section        0  exit.o(.text)
    .text                                    0x08000338   Section        0  sys_exit.o(.text)
    .text                                    0x08000344   Section        2  use_no_semi.o(.text)
    .text                                    0x08000346   Section        0  indicate_semi.o(.text)
    i.AD                                     0x08000346   Section        0  ai.o(i.AD)
    i.AD7606_Read_Reg                        0x08000350   Section        0  cl1606_spi.o(i.AD7606_Read_Reg)
    AD7606_Read_Reg                          0x08000351   Thumb Code    62  cl1606_spi.o(i.AD7606_Read_Reg)
    i.AD7606_Start_Convst                    0x08000394   Section        0  cl1606_spi.o(i.AD7606_Start_Convst)
    i.Boot_Delay                             0x080003bc   Section        0  main.o(i.Boot_Delay)
    Boot_Delay                               0x080003bd   Thumb Code    40  main.o(i.Boot_Delay)
    i.Byte_To_Float                          0x080003e8   Section        0  optical.o(i.Byte_To_Float)
    i.Cal_Period                             0x08000410   Section        0  syn.o(i.Cal_Period)
    i.Check_Syn_Error                        0x080004ec   Section        0  syn.o(i.Check_Syn_Error)
    i.Clear_Syn_Error                        0x0800053c   Section        0  syn.o(i.Clear_Syn_Error)
    i.Config_W5500                           0x080005a8   Section        0  internet.o(i.Config_W5500)
    Config_W5500                             0x080005a9   Thumb Code   130  internet.o(i.Config_W5500)
    i.Delay_Func                             0x0800062a   Section        0  cl1606_spi.o(i.Delay_Func)
    i.Delay_W5100                            0x08000636   Section        0  internet.o(i.Delay_W5100)
    i.Dyn_Comp_Timer_Init                    0x08000644   Section        0  optical.o(i.Dyn_Comp_Timer_Init)
    i.First_Rising_Edge                      0x08000658   Section        0  tim.o(i.First_Rising_Edge)
    i.Generate_Pulse                         0x0800080c   Section        0  tim.o(i.Generate_Pulse)
    i.Generate_Tx_Flag                       0x080008f0   Section        0  tim.o(i.Generate_Tx_Flag)
    i.Init_Data                              0x08000948   Section        0  para.o(i.Init_Data)
    i.Init_Hardware                          0x0800094a   Section        0  para.o(i.Init_Hardware)
    i.Init_Net                               0x08000954   Section        0  net.o(i.Init_Net)
    i.Net_Check                              0x08000aac   Section        0  net.o(i.Net_Check)
    i.Net_Comm                               0x08000bf0   Section        0  internet.o(i.Net_Comm)
    i.Net_Cycle                              0x08000c54   Section        0  internet.o(i.Net_Cycle)
    i.Net_DMA_Rx                             0x08000cfc   Section        0  internet.o(i.Net_DMA_Rx)
    i.Net_DMA_Tx                             0x08000dd4   Section        0  internet.o(i.Net_DMA_Tx)
    i.Net_DMA_rx_process                     0x08000ebc   Section        0  internet.o(i.Net_DMA_rx_process)
    i.Net_DMA_tx_process                     0x08001108   Section        0  internet.o(i.Net_DMA_tx_process)
    i.Net_Interrupt_Process                  0x0800131c   Section        0  internet.o(i.Net_Interrupt_Process)
    i.Net_Mode_Select                        0x080014f4   Section        0  internet.o(i.Net_Mode_Select)
    i.Net_Order                              0x080015e8   Section        0  net.o(i.Net_Order)
    i.Net_Para_Init                          0x08001654   Section        0  internet.o(i.Net_Para_Init)
    i.Net_Rx                                 0x08001720   Section        0  internet.o(i.Net_Rx)
    i.Net_State_Check                        0x080019cc   Section        0  internet.o(i.Net_State_Check)
    i.Net_Tx                                 0x08001a20   Section        0  internet.o(i.Net_Tx)
    i.Network_Init                           0x08001c90   Section        0  internet.o(i.Network_Init)
    i.Phase_Difference                       0x08001ec0   Section        0  syn.o(i.Phase_Difference)
    i.Primary_Syn_Check                      0x08001ee8   Section        0  syn.o(i.Primary_Syn_Check)
    i.Primary_Syn_Switch_Check               0x08002038   Section        0  syn.o(i.Primary_Syn_Switch_Check)
    i.Pulse_Syn_Error_Check                  0x08002078   Section        0  syn.o(i.Pulse_Syn_Error_Check)
    i.Read_AD_Sample                         0x08002160   Section        0  cl1606_spi.o(i.Read_AD_Sample)
    i.Read_Byte_W5500                        0x080021ac   Section        0  internet.o(i.Read_Byte_W5500)
    Read_Byte_W5500                          0x080021ad   Thumb Code   176  internet.o(i.Read_Byte_W5500)
    i.Read_SOCK_Byte1_W5500                  0x0800225c   Section        0  internet.o(i.Read_SOCK_Byte1_W5500)
    Read_SOCK_Byte1_W5500                    0x0800225d   Thumb Code   188  internet.o(i.Read_SOCK_Byte1_W5500)
    i.Read_SOCK_Byte2_W5500                  0x08002318   Section        0  internet.o(i.Read_SOCK_Byte2_W5500)
    Read_SOCK_Byte2_W5500                    0x08002319   Thumb Code   248  internet.o(i.Read_SOCK_Byte2_W5500)
    i.SCT_AD_R_Data                          0x08002410   Section        0  cl1606_spi.o(i.SCT_AD_R_Data)
    SCT_AD_R_Data                            0x08002411   Thumb Code    78  cl1606_spi.o(i.SCT_AD_R_Data)
    i.SPI1_DMA_Config                        0x08002460   Section        0  internet.o(i.SPI1_DMA_Config)
    i.SPI2_DMA_Config                        0x0800252c   Section        0  internet.o(i.SPI2_DMA_Config)
    i.SPI4_For_W5200_Init                    0x080025f8   Section        0  internet.o(i.SPI4_For_W5200_Init)
    SPI4_For_W5200_Init                      0x080025f9   Thumb Code   228  internet.o(i.SPI4_For_W5200_Init)
    i.SPI5_For_W5200_Init                    0x080026e8   Section        0  internet.o(i.SPI5_For_W5200_Init)
    SPI5_For_W5200_Init                      0x080026e9   Thumb Code   246  internet.o(i.SPI5_For_W5200_Init)
    i.SPIx_RW_One_Byte                       0x080027e8   Section        0  cl1606_spi.o(i.SPIx_RW_One_Byte)
    SPIx_RW_One_Byte                         0x080027e9   Thumb Code    62  cl1606_spi.o(i.SPIx_RW_One_Byte)
    i.Sec_Syn_Check                          0x0800282c   Section        0  syn.o(i.Sec_Syn_Check)
    i.SelectSyn                              0x080028a8   Section        0  syn.o(i.SelectSyn)
    i.SelectSynSource                        0x08002900   Section        0  optical.o(i.SelectSynSource)
    i.Set_Alpha_Delay                        0x08002a68   Section        0  tim.o(i.Set_Alpha_Delay)
    i.Socket_Correct_Addr                    0x08002ad0   Section        0  internet.o(i.Socket_Correct_Addr)
    i.Socket_Mode                            0x08002bf0   Section        0  internet.o(i.Socket_Mode)
    i.Soft_Delay                             0x08002c72   Section        0  para.o(i.Soft_Delay)
    i.Syn_Check                              0x08002c80   Section        0  syn.o(i.Syn_Check)
    i.SystemInit                             0x08002ca8   Section        0  system_gd32f4xx.o(i.SystemInit)
    i.TIM3_Update                            0x08002d60   Section        0  tim.o(i.TIM3_Update)
    i.TIM4_CC1                               0x08002e30   Section        0  tim.o(i.TIM4_CC1)
    i.TIM4_CC2                               0x08002eb0   Section        0  tim.o(i.TIM4_CC2)
    i.TIM4_CC4                               0x08002f30   Section        0  tim.o(i.TIM4_CC4)
    i.TIMER0_BRK_TIMER8_IRQHandler           0x08002fb0   Section        0  tim.o(i.TIMER0_BRK_TIMER8_IRQHandler)
    i.TIMER1_IRQHandler                      0x08003024   Section        0  tim.o(i.TIMER1_IRQHandler)
    i.TIMER2_IRQHandler                      0x08003038   Section        0  tim.o(i.TIMER2_IRQHandler)
    i.TIMER3_IRQHandler                      0x0800304c   Section        0  tim.o(i.TIMER3_IRQHandler)
    i.TIMER4_IRQHandler                      0x08003064   Section        0  tim.o(i.TIMER4_IRQHandler)
    i.W5200U1_RST_Init                       0x08003098   Section        0  internet.o(i.W5200U1_RST_Init)
    W5200U1_RST_Init                         0x08003099   Thumb Code    40  internet.o(i.W5200U1_RST_Init)
    i.W5200U1_RST_Off                        0x080030c4   Section        0  internet.o(i.W5200U1_RST_Off)
    W5200U1_RST_Off                          0x080030c5   Thumb Code    12  internet.o(i.W5200U1_RST_Off)
    i.W5200U2_RST_Init                       0x080030d4   Section        0  internet.o(i.W5200U2_RST_Init)
    W5200U2_RST_Init                         0x080030d5   Thumb Code    40  internet.o(i.W5200U2_RST_Init)
    i.W5200U2_RST_Off                        0x08003100   Section        0  internet.o(i.W5200U2_RST_Off)
    W5200U2_RST_Off                          0x08003101   Thumb Code    12  internet.o(i.W5200U2_RST_Off)
    i.Write_Byte1_W5500                      0x08003110   Section        0  internet.o(i.Write_Byte1_W5500)
    Write_Byte1_W5500                        0x08003111   Thumb Code   160  internet.o(i.Write_Byte1_W5500)
    i.Write_Byte2_W5500                      0x080031b0   Section        0  internet.o(i.Write_Byte2_W5500)
    Write_Byte2_W5500                        0x080031b1   Thumb Code   192  internet.o(i.Write_Byte2_W5500)
    i.Write_SOCK_Byte1_W5500                 0x08003270   Section        0  internet.o(i.Write_SOCK_Byte1_W5500)
    Write_SOCK_Byte1_W5500                   0x08003271   Thumb Code   172  internet.o(i.Write_SOCK_Byte1_W5500)
    i.Write_SOCK_Byte2_W5500                 0x0800331c   Section        0  internet.o(i.Write_SOCK_Byte2_W5500)
    Write_SOCK_Byte2_W5500                   0x0800331d   Thumb Code   204  internet.o(i.Write_SOCK_Byte2_W5500)
    i.Write_SOCK_Byte4_W5500                 0x080033e8   Section        0  internet.o(i.Write_SOCK_Byte4_W5500)
    Write_SOCK_Byte4_W5500                   0x080033e9   Thumb Code   274  internet.o(i.Write_SOCK_Byte4_W5500)
    i.Write_W5500                            0x080034fa   Section        0  internet.o(i.Write_W5500)
    Write_W5500                              0x080034fb   Thumb Code   192  internet.o(i.Write_W5500)
    i.dma_channel_disable                    0x080035ba   Section        0  gd32f4xx_dma.o(i.dma_channel_disable)
    i.dma_channel_enable                     0x080035da   Section        0  gd32f4xx_dma.o(i.dma_channel_enable)
    i.dma_channel_subperipheral_select       0x080035fa   Section        0  gd32f4xx_dma.o(i.dma_channel_subperipheral_select)
    i.dma_deinit                             0x08003620   Section        0  gd32f4xx_dma.o(i.dma_deinit)
    i.dma_flag_clear                         0x080036c4   Section        0  gd32f4xx_dma.o(i.dma_flag_clear)
    i.dma_flag_get                           0x08003702   Section        0  gd32f4xx_dma.o(i.dma_flag_get)
    i.dma_single_data_mode_init              0x08003750   Section        0  gd32f4xx_dma.o(i.dma_single_data_mode_init)
    i.dma_single_data_para_struct_init       0x080038a8   Section        0  gd32f4xx_dma.o(i.dma_single_data_para_struct_init)
    i.gpio_af_set                            0x080038ca   Section        0  gd32f4xx_gpio.o(i.gpio_af_set)
    i.gpio_bit_reset                         0x08003928   Section        0  gd32f4xx_gpio.o(i.gpio_bit_reset)
    i.gpio_bit_set                           0x0800392c   Section        0  gd32f4xx_gpio.o(i.gpio_bit_set)
    i.gpio_input_bit_get                     0x08003930   Section        0  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    i.gpio_mode_set                          0x08003940   Section        0  gd32f4xx_gpio.o(i.gpio_mode_set)
    i.gpio_output_options_set                0x0800398e   Section        0  gd32f4xx_gpio.o(i.gpio_output_options_set)
    i.main                                   0x080039d0   Section        0  main.o(i.main)
    i.nvic_priority_group_set                0x08003a00   Section        0  gd32f4xx_misc.o(i.nvic_priority_group_set)
    i.rcu_periph_clock_enable                0x08003a14   Section        0  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    i.rcu_periph_reset_disable               0x08003a38   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    i.rcu_periph_reset_enable                0x08003a5c   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    i.spi_crc_polynomial_set                 0x08003a80   Section        0  gd32f4xx_spi.o(i.spi_crc_polynomial_set)
    i.spi_disable                            0x08003a84   Section        0  gd32f4xx_spi.o(i.spi_disable)
    i.spi_dma_disable                        0x08003a8e   Section        0  gd32f4xx_spi.o(i.spi_dma_disable)
    i.spi_dma_enable                         0x08003aa4   Section        0  gd32f4xx_spi.o(i.spi_dma_enable)
    i.spi_enable                             0x08003aba   Section        0  gd32f4xx_spi.o(i.spi_enable)
    i.spi_i2s_data_receive                   0x08003ac4   Section        0  gd32f4xx_spi.o(i.spi_i2s_data_receive)
    i.spi_i2s_data_transmit                  0x08003acc   Section        0  gd32f4xx_spi.o(i.spi_i2s_data_transmit)
    i.spi_i2s_deinit                         0x08003ad0   Section        0  gd32f4xx_spi.o(i.spi_i2s_deinit)
    i.spi_i2s_flag_get                       0x08003b7c   Section        0  gd32f4xx_spi.o(i.spi_i2s_flag_get)
    i.spi_init                               0x08003b8c   Section        0  gd32f4xx_spi.o(i.spi_init)
    i.spi_nss_output_enable                  0x08003bbe   Section        0  gd32f4xx_spi.o(i.spi_nss_output_enable)
    i.system_clock_200m_25m_hxtal            0x08003bc8   Section        0  system_gd32f4xx.o(i.system_clock_200m_25m_hxtal)
    system_clock_200m_25m_hxtal              0x08003bc9   Thumb Code   240  system_gd32f4xx.o(i.system_clock_200m_25m_hxtal)
    i.system_clock_config                    0x08003cc4   Section        0  system_gd32f4xx.o(i.system_clock_config)
    system_clock_config                      0x08003cc5   Thumb Code     8  system_gd32f4xx.o(i.system_clock_config)
    i.timer_disable                          0x08003ccc   Section        0  gd32f4xx_timer.o(i.timer_disable)
    i.timer_enable                           0x08003cd6   Section        0  gd32f4xx_timer.o(i.timer_enable)
    i.timer_flag_clear                       0x08003ce0   Section        0  gd32f4xx_timer.o(i.timer_flag_clear)
    i.timer_interrupt_disable                0x08003ce6   Section        0  gd32f4xx_timer.o(i.timer_interrupt_disable)
    i.timer_interrupt_enable                 0x08003cee   Section        0  gd32f4xx_timer.o(i.timer_interrupt_enable)
    i.timer_interrupt_flag_clear             0x08003cf6   Section        0  gd32f4xx_timer.o(i.timer_interrupt_flag_clear)
    i.timer_interrupt_flag_get               0x08003cfc   Section        0  gd32f4xx_timer.o(i.timer_interrupt_flag_get)
    x$fpl$fpinit                             0x08003d14   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08003d14   Number         0  fpinit.o(x$fpl$fpinit)
    .data                                    0x20000000   Section      112  internet.o(.data)
    .data                                    0x20000070   Section        4  net.o(.data)
    .data                                    0x20000074   Section      612  optical.o(.data)
    .data                                    0x200002d8   Section      660  pub.o(.data)
    .data                                    0x2000056c   Section       40  tim.o(.data)
    .bss                                     0x20000594   Section    26428  internet.o(.bss)
    .bss                                     0x20006cd0   Section    17640  pub.o(.bss)
    .bss                                     0x2000b1b8   Section       28  tim.o(.bss)
    .bss                                     0x2000b1d4   Section       96  libspace.o(.bss)
    HEAP                                     0x2000b238   Section     1024  startup_gd32f450.o(HEAP)
    Heap_Mem                                 0x2000b238   Data        1024  startup_gd32f450.o(HEAP)
    STACK                                    0x2000b638   Section     1024  startup_gd32f450.o(STACK)
    Stack_Mem                                0x2000b638   Data        1024  startup_gd32f450.o(STACK)
    __initial_sp                             0x2000ba38   Data           0  startup_gd32f450.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __rt_locale                               - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_numeric                           - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_gd32f450.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_gd32f450.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_gd32f450.o(RESET)
    __main                                   0x080001ad   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080001b5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080001b5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080001b5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080001c3   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001e9   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x080001e9   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000245   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x08000261   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000263   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_alloca_1                   0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000267   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000269   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0800026b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0800026b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x0800026b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x0800026b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x0800026b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x0800026b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x0800026b   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x0800026d   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x0800026d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x0800026d   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000273   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000273   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000277   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000277   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800027f   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000281   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000281   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000285   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x0800028d   Thumb Code     8  startup_gd32f450.o(.text)
    NMI_Handler                              0x08000295   Thumb Code     2  startup_gd32f450.o(.text)
    HardFault_Handler                        0x08000297   Thumb Code     2  startup_gd32f450.o(.text)
    MemManage_Handler                        0x08000299   Thumb Code     2  startup_gd32f450.o(.text)
    BusFault_Handler                         0x0800029b   Thumb Code     2  startup_gd32f450.o(.text)
    UsageFault_Handler                       0x0800029d   Thumb Code     2  startup_gd32f450.o(.text)
    SVC_Handler                              0x0800029f   Thumb Code     2  startup_gd32f450.o(.text)
    DebugMon_Handler                         0x080002a1   Thumb Code     2  startup_gd32f450.o(.text)
    PendSV_Handler                           0x080002a3   Thumb Code     2  startup_gd32f450.o(.text)
    SysTick_Handler                          0x080002a5   Thumb Code     2  startup_gd32f450.o(.text)
    ADC_IRQHandler                           0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    CAN0_EWMC_IRQHandler                     0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    CAN0_RX0_IRQHandler                      0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    CAN0_RX1_IRQHandler                      0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    CAN0_TX_IRQHandler                       0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    CAN1_EWMC_IRQHandler                     0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    CAN1_RX0_IRQHandler                      0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    CAN1_RX1_IRQHandler                      0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    CAN1_TX_IRQHandler                       0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    DCI_IRQHandler                           0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    DMA0_Channel0_IRQHandler                 0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    DMA0_Channel1_IRQHandler                 0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    DMA0_Channel2_IRQHandler                 0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    DMA0_Channel3_IRQHandler                 0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    DMA0_Channel4_IRQHandler                 0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    DMA0_Channel5_IRQHandler                 0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    DMA0_Channel6_IRQHandler                 0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    DMA0_Channel7_IRQHandler                 0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    DMA1_Channel0_IRQHandler                 0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    DMA1_Channel1_IRQHandler                 0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    DMA1_Channel2_IRQHandler                 0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    DMA1_Channel3_IRQHandler                 0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    DMA1_Channel4_IRQHandler                 0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    DMA1_Channel5_IRQHandler                 0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    DMA1_Channel6_IRQHandler                 0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    DMA1_Channel7_IRQHandler                 0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    ENET_IRQHandler                          0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    ENET_WKUP_IRQHandler                     0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    EXMC_IRQHandler                          0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    EXTI0_IRQHandler                         0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    EXTI10_15_IRQHandler                     0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    EXTI1_IRQHandler                         0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    EXTI2_IRQHandler                         0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    EXTI3_IRQHandler                         0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    EXTI4_IRQHandler                         0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    EXTI5_9_IRQHandler                       0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    FMC_IRQHandler                           0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    FPU_IRQHandler                           0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    I2C0_ER_IRQHandler                       0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    I2C0_EV_IRQHandler                       0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    I2C1_ER_IRQHandler                       0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    I2C1_EV_IRQHandler                       0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    I2C2_ER_IRQHandler                       0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    I2C2_EV_IRQHandler                       0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    IPA_IRQHandler                           0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    LVD_IRQHandler                           0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    RCU_CTC_IRQHandler                       0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    RTC_Alarm_IRQHandler                     0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    RTC_WKUP_IRQHandler                      0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    SDIO_IRQHandler                          0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    SPI0_IRQHandler                          0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    SPI1_IRQHandler                          0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    SPI2_IRQHandler                          0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    SPI3_IRQHandler                          0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    SPI4_IRQHandler                          0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    SPI5_IRQHandler                          0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    TAMPER_STAMP_IRQHandler                  0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    TIMER0_Channel_IRQHandler                0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    TIMER0_TRG_CMT_TIMER10_IRQHandler        0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    TIMER0_UP_TIMER9_IRQHandler              0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    TIMER5_DAC_IRQHandler                    0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    TIMER6_IRQHandler                        0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    TIMER7_BRK_TIMER11_IRQHandler            0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    TIMER7_Channel_IRQHandler                0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    TIMER7_TRG_CMT_TIMER13_IRQHandler        0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    TIMER7_UP_TIMER12_IRQHandler             0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    TLI_ER_IRQHandler                        0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    TLI_IRQHandler                           0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    TRNG_IRQHandler                          0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    UART3_IRQHandler                         0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    UART4_IRQHandler                         0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    UART6_IRQHandler                         0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    UART7_IRQHandler                         0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    USART0_IRQHandler                        0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    USART1_IRQHandler                        0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    USART2_IRQHandler                        0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    USART5_IRQHandler                        0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    USBFS_IRQHandler                         0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    USBFS_WKUP_IRQHandler                    0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    USBHS_EP1_In_IRQHandler                  0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    USBHS_EP1_Out_IRQHandler                 0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    USBHS_IRQHandler                         0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    USBHS_WKUP_IRQHandler                    0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    WWDGT_IRQHandler                         0x080002a7   Thumb Code     0  startup_gd32f450.o(.text)
    __user_initial_stackheap                 0x080002a9   Thumb Code    10  startup_gd32f450.o(.text)
    __use_two_region_memory                  0x080002cd   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080002cf   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080002d1   Thumb Code     2  heapauxi.o(.text)
    __user_libspace                          0x080002d5   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080002d5   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080002d5   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x080002dd   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08000327   Thumb Code    18  exit.o(.text)
    _sys_exit                                0x08000339   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08000345   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000345   Thumb Code     2  use_no_semi.o(.text)
    AD                                       0x08000347   Thumb Code    10  ai.o(i.AD)
    __semihosting_library_function           0x08000347   Thumb Code     0  indicate_semi.o(.text)
    AD7606_Start_Convst                      0x08000395   Thumb Code    34  cl1606_spi.o(i.AD7606_Start_Convst)
    Byte_To_Float                            0x080003e9   Thumb Code    38  optical.o(i.Byte_To_Float)
    Cal_Period                               0x08000411   Thumb Code   190  syn.o(i.Cal_Period)
    Check_Syn_Error                          0x080004ed   Thumb Code    52  syn.o(i.Check_Syn_Error)
    Clear_Syn_Error                          0x0800053d   Thumb Code    56  syn.o(i.Clear_Syn_Error)
    Delay_Func                               0x0800062b   Thumb Code    12  cl1606_spi.o(i.Delay_Func)
    Delay_W5100                              0x08000637   Thumb Code    12  internet.o(i.Delay_W5100)
    Dyn_Comp_Timer_Init                      0x08000645   Thumb Code    10  optical.o(i.Dyn_Comp_Timer_Init)
    First_Rising_Edge                        0x08000659   Thumb Code   372  tim.o(i.First_Rising_Edge)
    Generate_Pulse                           0x0800080d   Thumb Code   218  tim.o(i.Generate_Pulse)
    Generate_Tx_Flag                         0x080008f1   Thumb Code    64  tim.o(i.Generate_Tx_Flag)
    Init_Data                                0x08000949   Thumb Code     2  para.o(i.Init_Data)
    Init_Hardware                            0x0800094b   Thumb Code     8  para.o(i.Init_Hardware)
    Init_Net                                 0x08000955   Thumb Code   314  net.o(i.Init_Net)
    Net_Check                                0x08000aad   Thumb Code   264  net.o(i.Net_Check)
    Net_Comm                                 0x08000bf1   Thumb Code    92  internet.o(i.Net_Comm)
    Net_Cycle                                0x08000c55   Thumb Code   148  internet.o(i.Net_Cycle)
    Net_DMA_Rx                               0x08000cfd   Thumb Code   182  internet.o(i.Net_DMA_Rx)
    Net_DMA_Tx                               0x08000dd5   Thumb Code   200  internet.o(i.Net_DMA_Tx)
    Net_DMA_rx_process                       0x08000ebd   Thumb Code   540  internet.o(i.Net_DMA_rx_process)
    Net_DMA_tx_process                       0x08001109   Thumb Code   496  internet.o(i.Net_DMA_tx_process)
    Net_Interrupt_Process                    0x0800131d   Thumb Code   432  internet.o(i.Net_Interrupt_Process)
    Net_Mode_Select                          0x080014f5   Thumb Code   224  internet.o(i.Net_Mode_Select)
    Net_Order                                0x080015e9   Thumb Code    76  net.o(i.Net_Order)
    Net_Para_Init                            0x08001655   Thumb Code   138  internet.o(i.Net_Para_Init)
    Net_Rx                                   0x08001721   Thumb Code   656  internet.o(i.Net_Rx)
    Net_State_Check                          0x080019cd   Thumb Code    70  internet.o(i.Net_State_Check)
    Net_Tx                                   0x08001a21   Thumb Code   600  internet.o(i.Net_Tx)
    Network_Init                             0x08001c91   Thumb Code   512  internet.o(i.Network_Init)
    Phase_Difference                         0x08001ec1   Thumb Code    36  syn.o(i.Phase_Difference)
    Primary_Syn_Check                        0x08001ee9   Thumb Code   282  syn.o(i.Primary_Syn_Check)
    Primary_Syn_Switch_Check                 0x08002039   Thumb Code    52  syn.o(i.Primary_Syn_Switch_Check)
    Pulse_Syn_Error_Check                    0x08002079   Thumb Code   184  syn.o(i.Pulse_Syn_Error_Check)
    Read_AD_Sample                           0x08002161   Thumb Code    70  cl1606_spi.o(i.Read_AD_Sample)
    SPI1_DMA_Config                          0x08002461   Thumb Code   184  internet.o(i.SPI1_DMA_Config)
    SPI2_DMA_Config                          0x0800252d   Thumb Code   184  internet.o(i.SPI2_DMA_Config)
    Sec_Syn_Check                            0x0800282d   Thumb Code    98  syn.o(i.Sec_Syn_Check)
    SelectSyn                                0x080028a9   Thumb Code    66  syn.o(i.SelectSyn)
    SelectSynSource                          0x08002901   Thumb Code   342  optical.o(i.SelectSynSource)
    Set_Alpha_Delay                          0x08002a69   Thumb Code    92  tim.o(i.Set_Alpha_Delay)
    Socket_Correct_Addr                      0x08002ad1   Thumb Code   266  internet.o(i.Socket_Correct_Addr)
    Socket_Mode                              0x08002bf1   Thumb Code   130  internet.o(i.Socket_Mode)
    Soft_Delay                               0x08002c73   Thumb Code    12  para.o(i.Soft_Delay)
    Syn_Check                                0x08002c81   Thumb Code    36  syn.o(i.Syn_Check)
    SystemInit                               0x08002ca9   Thumb Code   168  system_gd32f4xx.o(i.SystemInit)
    TIM3_Update                              0x08002d61   Thumb Code   162  tim.o(i.TIM3_Update)
    TIM4_CC1                                 0x08002e31   Thumb Code    84  tim.o(i.TIM4_CC1)
    TIM4_CC2                                 0x08002eb1   Thumb Code    84  tim.o(i.TIM4_CC2)
    TIM4_CC4                                 0x08002f31   Thumb Code    84  tim.o(i.TIM4_CC4)
    TIMER0_BRK_TIMER8_IRQHandler             0x08002fb1   Thumb Code    84  tim.o(i.TIMER0_BRK_TIMER8_IRQHandler)
    TIMER1_IRQHandler                        0x08003025   Thumb Code    18  tim.o(i.TIMER1_IRQHandler)
    TIMER2_IRQHandler                        0x08003039   Thumb Code    16  tim.o(i.TIMER2_IRQHandler)
    TIMER3_IRQHandler                        0x0800304d   Thumb Code    18  tim.o(i.TIMER3_IRQHandler)
    TIMER4_IRQHandler                        0x08003065   Thumb Code    46  tim.o(i.TIMER4_IRQHandler)
    dma_channel_disable                      0x080035bb   Thumb Code    32  gd32f4xx_dma.o(i.dma_channel_disable)
    dma_channel_enable                       0x080035db   Thumb Code    32  gd32f4xx_dma.o(i.dma_channel_enable)
    dma_channel_subperipheral_select         0x080035fb   Thumb Code    38  gd32f4xx_dma.o(i.dma_channel_subperipheral_select)
    dma_deinit                               0x08003621   Thumb Code   164  gd32f4xx_dma.o(i.dma_deinit)
    dma_flag_clear                           0x080036c5   Thumb Code    62  gd32f4xx_dma.o(i.dma_flag_clear)
    dma_flag_get                             0x08003703   Thumb Code    76  gd32f4xx_dma.o(i.dma_flag_get)
    dma_single_data_mode_init                0x08003751   Thumb Code   340  gd32f4xx_dma.o(i.dma_single_data_mode_init)
    dma_single_data_para_struct_init         0x080038a9   Thumb Code    34  gd32f4xx_dma.o(i.dma_single_data_para_struct_init)
    gpio_af_set                              0x080038cb   Thumb Code    94  gd32f4xx_gpio.o(i.gpio_af_set)
    gpio_bit_reset                           0x08003929   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_reset)
    gpio_bit_set                             0x0800392d   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_set)
    gpio_input_bit_get                       0x08003931   Thumb Code    16  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    gpio_mode_set                            0x08003941   Thumb Code    78  gd32f4xx_gpio.o(i.gpio_mode_set)
    gpio_output_options_set                  0x0800398f   Thumb Code    66  gd32f4xx_gpio.o(i.gpio_output_options_set)
    main                                     0x080039d1   Thumb Code    42  main.o(i.main)
    nvic_priority_group_set                  0x08003a01   Thumb Code    10  gd32f4xx_misc.o(i.nvic_priority_group_set)
    rcu_periph_clock_enable                  0x08003a15   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    rcu_periph_reset_disable                 0x08003a39   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    rcu_periph_reset_enable                  0x08003a5d   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    spi_crc_polynomial_set                   0x08003a81   Thumb Code     4  gd32f4xx_spi.o(i.spi_crc_polynomial_set)
    spi_disable                              0x08003a85   Thumb Code    10  gd32f4xx_spi.o(i.spi_disable)
    spi_dma_disable                          0x08003a8f   Thumb Code    22  gd32f4xx_spi.o(i.spi_dma_disable)
    spi_dma_enable                           0x08003aa5   Thumb Code    22  gd32f4xx_spi.o(i.spi_dma_enable)
    spi_enable                               0x08003abb   Thumb Code    10  gd32f4xx_spi.o(i.spi_enable)
    spi_i2s_data_receive                     0x08003ac5   Thumb Code     8  gd32f4xx_spi.o(i.spi_i2s_data_receive)
    spi_i2s_data_transmit                    0x08003acd   Thumb Code     4  gd32f4xx_spi.o(i.spi_i2s_data_transmit)
    spi_i2s_deinit                           0x08003ad1   Thumb Code   162  gd32f4xx_spi.o(i.spi_i2s_deinit)
    spi_i2s_flag_get                         0x08003b7d   Thumb Code    16  gd32f4xx_spi.o(i.spi_i2s_flag_get)
    spi_init                                 0x08003b8d   Thumb Code    50  gd32f4xx_spi.o(i.spi_init)
    spi_nss_output_enable                    0x08003bbf   Thumb Code    10  gd32f4xx_spi.o(i.spi_nss_output_enable)
    timer_disable                            0x08003ccd   Thumb Code    10  gd32f4xx_timer.o(i.timer_disable)
    timer_enable                             0x08003cd7   Thumb Code    10  gd32f4xx_timer.o(i.timer_enable)
    timer_flag_clear                         0x08003ce1   Thumb Code     6  gd32f4xx_timer.o(i.timer_flag_clear)
    timer_interrupt_disable                  0x08003ce7   Thumb Code     8  gd32f4xx_timer.o(i.timer_interrupt_disable)
    timer_interrupt_enable                   0x08003cef   Thumb Code     8  gd32f4xx_timer.o(i.timer_interrupt_enable)
    timer_interrupt_flag_clear               0x08003cf7   Thumb Code     6  gd32f4xx_timer.o(i.timer_interrupt_flag_clear)
    timer_interrupt_flag_get                 0x08003cfd   Thumb Code    24  gd32f4xx_timer.o(i.timer_interrupt_flag_get)
    _fp_init                                 0x08003d15   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08003d1d   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08003d1d   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    Region$$Table$$Base                      0x08003d20   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08003d40   Number         0  anon$$obj.o(Region$$Table)
    Network_Sub_Mask                         0x20000000   Data           4  internet.o(.data)
    Network_Phy_Addr                         0x20000004   Data           6  internet.o(.data)
    Net_Check_Delay                          0x2000000a   Data           2  internet.o(.data)
    Net_Rx_Addr                              0x2000000c   Data           4  internet.o(.data)
    Net_Tx_Addr                              0x20000010   Data           4  internet.o(.data)
    Net_Socket_Rx_Addr                       0x20000014   Data           8  internet.o(.data)
    Net_Socket_Tx_Addr                       0x2000001c   Data           8  internet.o(.data)
    Net_Enable                               0x20000024   Data           4  internet.o(.data)
    Net_Mode                                 0x20000028   Data           4  internet.o(.data)
    Net_Socket_State                         0x2000002c   Data           4  internet.o(.data)
    Net_Socket_Data                          0x20000030   Data           4  internet.o(.data)
    Net_State                                0x20000034   Data           2  internet.o(.data)
    Net_Tx_Flag                              0x20000036   Data           4  internet.o(.data)
    Net_Rx_Flag                              0x2000003a   Data           4  internet.o(.data)
    Net_Rx_size                              0x2000003e   Data           4  internet.o(.data)
    Net_Tx_size                              0x20000042   Data           8  internet.o(.data)
    Net_Tx_Addr_Offset                       0x2000004a   Data           8  internet.o(.data)
    Net_Rx_Addr_Offset                       0x20000052   Data           8  internet.o(.data)
    Net_Receiving_Socket                     0x2000005a   Data           2  internet.o(.data)
    Net_Sending_Socket                       0x2000005c   Data           2  internet.o(.data)
    Net_Rx_Delay                             0x2000005e   Data           4  internet.o(.data)
    Net_Tx_Delay                             0x20000062   Data           4  internet.o(.data)
    DMA_Tx_Size                              0x20000068   Data           8  internet.o(.data)
    Net_Tx_Count                             0x20000070   Data           4  net.o(.data)
    Online_Source                            0x20000074   Data           8  optical.o(.data)
    Optical_Rx_Flag                          0x2000007c   Data           8  optical.o(.data)
    Optical_Pulse_Flag                       0x20000084   Data           8  optical.o(.data)
    Optical_Syn                              0x2000008c   Data           8  optical.o(.data)
    Optical_Standby_Flag                     0x20000094   Data           8  optical.o(.data)
    Ch_Online_Flag                           0x2000009c   Data           8  optical.o(.data)
    Ch_Online_Delay                          0x200000a4   Data           2  optical.o(.data)
    Unique_Scr_Flag                          0x200000a6   Data           1  optical.o(.data)
    Unique_Scr_Delay                         0x200000a8   Data           2  optical.o(.data)
    Optical_Tx_Flag                          0x200000aa   Data           1  optical.o(.data)
    ROM_Write_Result                         0x200000ab   Data           1  optical.o(.data)
    Result_Flag                              0x200000ac   Data           1  optical.o(.data)
    Optical_Comm_Err_Flag                    0x200000ad   Data           1  optical.o(.data)
    Online_Comm_Err_Flag                     0x200000ae   Data           1  optical.o(.data)
    Optical_Comm_Err_Delay                   0x200000b0   Data           2  optical.o(.data)
    Optical_OK_Num                           0x200000b2   Data           2  optical.o(.data)
    Standby_Scr_Comm_Err_Delay               0x200000b4   Data           2  optical.o(.data)
    Standby_Online_Comm_Err_Delay            0x200000b6   Data           2  optical.o(.data)
    Standby_Delay                            0x200000b8   Data           2  optical.o(.data)
    Pulse_Off_Delay                          0x200000ba   Data           2  optical.o(.data)
    Reset_Delay                              0x200000bc   Data           4  optical.o(.data)
    Regulator_Offline_Count                  0x200000c0   Data           2  optical.o(.data)
    ScrSynError                              0x200000c2   Data           8  optical.o(.data)
    Comm_Count                               0x200000cc   Data           4  optical.o(.data)
    OC_D                                     0x200000d0   Data           1  optical.o(.data)
    t_Refresh_Delay                          0x200000d4   Data           4  optical.o(.data)
    m_auchCRCHi                              0x200000d8   Data         256  optical.o(.data)
    m_auchCRCLo                              0x200001d8   Data         256  optical.o(.data)
    Online_Delay                             0x200002d8   Data           2  pub.o(.data)
    DO_Lock_Flag                             0x200002da   Data           1  pub.o(.data)
    DO_Lock_Delay                            0x200002dc   Data           2  pub.o(.data)
    Current_Pulse                            0x200002de   Data           1  pub.o(.data)
    Scr_Fan_Err_Flag                         0x200002df   Data           1  pub.o(.data)
    Scr_Exit_Flag                            0x200002e0   Data           1  pub.o(.data)
    SynSource                                0x200002e1   Data           1  pub.o(.data)
    Ifd                                      0x200002e4   Data           4  pub.o(.data)
    Vfd                                      0x200002e8   Data           4  pub.o(.data)
    Alpha                                    0x200002ec   Data           4  pub.o(.data)
    Q                                        0x200002f0   Data           4  pub.o(.data)
    P                                        0x200002f4   Data           4  pub.o(.data)
    F                                        0x200002f8   Data           4  pub.o(.data)
    Period                                   0x200002fc   Data           4  pub.o(.data)
    Ifd_av                                   0x20000300   Data           4  pub.o(.data)
    Vfd_av                                   0x20000304   Data           4  pub.o(.data)
    Qav                                      0x20000308   Data           4  pub.o(.data)
    Pav                                      0x2000030c   Data           4  pub.o(.data)
    Fav                                      0x20000310   Data           4  pub.o(.data)
    Period_av                                0x20000314   Data           4  pub.o(.data)
    Avg_Ctrl_Coef                            0x20000318   Data           4  pub.o(.data)
    Avg_Coef                                 0x2000031c   Data           4  pub.o(.data)
    Kvrefq                                   0x20000320   Data           4  pub.o(.data)
    Over_Temperature_Flag                    0x20000324   Data           4  pub.o(.data)
    Syn_F_Delay                              0x20000328   Data           4  pub.o(.data)
    Low_F_Flag                               0x2000032c   Data           1  pub.o(.data)
    Low_F_Count                              0x20000330   Data           4  pub.o(.data)
    Low_F_Trig_Delay                         0x20000334   Data           4  pub.o(.data)
    Syn_A_Int_Time                           0x20000338   Data           4  pub.o(.data)
    Syn_B_Int_Time                           0x2000033c   Data           4  pub.o(.data)
    Syn_C_Int_Time                           0x20000340   Data           4  pub.o(.data)
    Syn_P_Int_Time                           0x20000344   Data           4  pub.o(.data)
    Syn_AB_Time                              0x20000348   Data           4  pub.o(.data)
    Syn_BC_Time                              0x2000034c   Data           4  pub.o(.data)
    Syn_CA_Time                              0x20000350   Data           4  pub.o(.data)
    Syn_Int_Time                             0x20000354   Data           4  pub.o(.data)
    Syn_A_Work_Flag                          0x20000358   Data           1  pub.o(.data)
    Syn_B_Work_Flag                          0x20000359   Data           1  pub.o(.data)
    Syn_C_Work_Flag                          0x2000035a   Data           1  pub.o(.data)
    Syn_A_Err_Flag                           0x2000035b   Data           1  pub.o(.data)
    Syn_B_Err_Flag                           0x2000035c   Data           1  pub.o(.data)
    Syn_C_Err_Flag                           0x2000035d   Data           1  pub.o(.data)
    Sec_A_Flag                               0x2000035e   Data           1  pub.o(.data)
    Sec_B_Flag                               0x2000035f   Data           1  pub.o(.data)
    Sec_C_Flag                               0x20000360   Data           1  pub.o(.data)
    Syn_Error_Flag                           0x20000361   Data           1  pub.o(.data)
    Sec_A_Count                              0x20000364   Data           4  pub.o(.data)
    Sec_B_Count                              0x20000368   Data           4  pub.o(.data)
    Sec_C_Count                              0x2000036c   Data           4  pub.o(.data)
    Syn_A_Count                              0x20000370   Data           4  pub.o(.data)
    Syn_B_Count                              0x20000374   Data           4  pub.o(.data)
    Syn_C_Count                              0x20000378   Data           4  pub.o(.data)
    Syn_Pulse_Count                          0x2000037c   Data           4  pub.o(.data)
    DI_Data                                  0x20000380   Data           8  pub.o(.data)
    DO_Data                                  0x20000388   Data           8  pub.o(.data)
    Running_Count                            0x20000390   Data           4  pub.o(.data)
    Running_Flag                             0x20000394   Data           4  pub.o(.data)
    Vfd_AD_Const                             0x20000398   Data           4  pub.o(.data)
    V_AD_Const                               0x2000039c   Data           4  pub.o(.data)
    I_AD_Const                               0x200003a0   Data           4  pub.o(.data)
    Vfd_n                                    0x200003a4   Data           4  pub.o(.data)
    V_n                                      0x200003a8   Data           4  pub.o(.data)
    I_n                                      0x200003ac   Data           4  pub.o(.data)
    S_n                                      0x200003b0   Data           4  pub.o(.data)
    AD_Err_Flag                              0x200003b4   Data           1  pub.o(.data)
    SCR_Error_Flag                           0x200003b5   Data           1  pub.o(.data)
    Fuse_Flag                                0x200003b6   Data           1  pub.o(.data)
    Fuse_A_Flag                              0x200003b7   Data           1  pub.o(.data)
    Fuse_B_Flag                              0x200003b8   Data           1  pub.o(.data)
    Fuse_C_Flag                              0x200003b9   Data           1  pub.o(.data)
    Fuse_An_Flag                             0x200003ba   Data           1  pub.o(.data)
    Fuse_Bn_Flag                             0x200003bb   Data           1  pub.o(.data)
    Fuse_Cn_Flag                             0x200003bc   Data           1  pub.o(.data)
    Phase_A_Flag                             0x200003bd   Data           1  pub.o(.data)
    Phase_B_Flag                             0x200003be   Data           1  pub.o(.data)
    Phase_C_Flag                             0x200003bf   Data           1  pub.o(.data)
    Phase_A_Delay                            0x200003c0   Data           2  pub.o(.data)
    Phase_B_Delay                            0x200003c2   Data           2  pub.o(.data)
    Phase_C_Delay                            0x200003c4   Data           2  pub.o(.data)
    Fan_Flag                                 0x200003c6   Data           1  pub.o(.data)
    Fan_On_Flag                              0x200003c7   Data           1  pub.o(.data)
    Pulse_Pointer                            0x200003c8   Data           2  pub.o(.data)
    Phase_Offset                             0x200003ca   Data           2  pub.o(.data)
    Rising_Edge_Flag                         0x200003cc   Data           4  pub.o(.data)
    Pulse_Enabled_Flag                       0x200003d0   Data           1  pub.o(.data)
    Pulse_Test_Flag                          0x200003d1   Data           1  pub.o(.data)
    Test_Pulse                               0x200003d2   Data           1  pub.o(.data)
    Va                                       0x200003d4   Data           4  pub.o(.data)
    Vb                                       0x200003d8   Data           4  pub.o(.data)
    Vc                                       0x200003dc   Data           4  pub.o(.data)
    Vabc                                     0x200003e0   Data           4  pub.o(.data)
    Vav                                      0x200003e4   Data           4  pub.o(.data)
    Ia                                       0x200003e8   Data           4  pub.o(.data)
    Ib                                       0x200003ec   Data           4  pub.o(.data)
    Ic                                       0x200003f0   Data           4  pub.o(.data)
    Iabc                                     0x200003f4   Data           4  pub.o(.data)
    Iav                                      0x200003f8   Data           4  pub.o(.data)
    Iap                                      0x200003fc   Data           4  pub.o(.data)
    Ibp                                      0x20000400   Data           4  pub.o(.data)
    Icp                                      0x20000404   Data           4  pub.o(.data)
    Ian                                      0x20000408   Data           4  pub.o(.data)
    Ibn                                      0x2000040c   Data           4  pub.o(.data)
    Icn                                      0x20000410   Data           4  pub.o(.data)
    Tap                                      0x20000414   Data           4  pub.o(.data)
    Tbp                                      0x20000418   Data           4  pub.o(.data)
    Tcp                                      0x2000041c   Data           4  pub.o(.data)
    Tan                                      0x20000420   Data           4  pub.o(.data)
    Tbn                                      0x20000424   Data           4  pub.o(.data)
    Tcn                                      0x20000428   Data           4  pub.o(.data)
    Tscr                                     0x2000042c   Data           4  pub.o(.data)
    Tin                                      0x20000430   Data           4  pub.o(.data)
    Tout                                     0x20000434   Data           4  pub.o(.data)
    Vabc_Temp                                0x20000438   Data           8  pub.o(.data)
    Iabc_Temp                                0x20000440   Data           8  pub.o(.data)
    Vfd_Temp                                 0x20000448   Data           8  pub.o(.data)
    Ifd_Temp                                 0x20000450   Data           8  pub.o(.data)
    uchNodeNum                               0x20000458   Data           1  pub.o(.data)
    Scr_FCR_Flag                             0x20000459   Data           1  pub.o(.data)
    AI_Flag                                  0x2000045a   Data           1  pub.o(.data)
    Ref_Up_Flag                              0x2000045b   Data           1  pub.o(.data)
    Ref_Down_Flag                            0x2000045c   Data           1  pub.o(.data)
    DeEx_Flag                                0x2000045d   Data           1  pub.o(.data)
    Ref                                      0x20000460   Data           4  pub.o(.data)
    Ref_Ctrl                                 0x20000464   Data           4  pub.o(.data)
    Ref_Step_Up_Adder                        0x20000468   Data           4  pub.o(.data)
    Ref_Step_Down_Adder                      0x2000046c   Data           4  pub.o(.data)
    Ref_Step                                 0x20000470   Data           4  pub.o(.data)
    Ref_Step_Limit                           0x20000474   Data           4  pub.o(.data)
    Ref_Max                                  0x20000478   Data           4  pub.o(.data)
    Ref_Min                                  0x2000047c   Data           4  pub.o(.data)
    Alpha_E                                  0x20000480   Data           4  pub.o(.data)
    PID_X                                    0x20000484   Data           8  pub.o(.data)
    PID_K                                    0x2000048c   Data           4  pub.o(.data)
    Syn_T                                    0x20000490   Data           6  pub.o(.data)
    Ifd_Index                                0x20000496   Data           2  pub.o(.data)
    T_Ifd                                    0x20000498   Data           4  pub.o(.data)
    Scr_Control_Enabled_Flag                 0x2000049c   Data           1  pub.o(.data)
    Alpha_Ifdav_Enabled_Flag                 0x2000049d   Data           1  pub.o(.data)
    Scr_Ifdav_Enabled_Flag                   0x2000049e   Data           1  pub.o(.data)
    ROM_Flag                                 0x2000049f   Data           1  pub.o(.data)
    Ifd_Const                                0x200004a0   Data           4  pub.o(.data)
    Vfd_Const                                0x200004a4   Data           4  pub.o(.data)
    V_Const                                  0x200004a8   Data           4  pub.o(.data)
    CT_n                                     0x200004ac   Data           1  pub.o(.data)
    V_Range                                  0x200004ad   Data           1  pub.o(.data)
    Alpha_H                                  0x200004b0   Data           4  pub.o(.data)
    Scr_Online_Flag                          0x200004b4   Data           1  pub.o(.data)
    Bak_Online_Flag                          0x200004b5   Data           1  pub.o(.data)
    Scr_Control_Flag                         0x200004b6   Data           1  pub.o(.data)
    Scr_Switch_Flag                          0x200004b7   Data           1  pub.o(.data)
    Scr_Switch_Delay                         0x200004b8   Data           4  pub.o(.data)
    Scr_Offline_Delay                        0x200004bc   Data           4  pub.o(.data)
    Scr_Online_Delay                         0x200004c0   Data           4  pub.o(.data)
    Control_Source                           0x200004c4   Data           7  pub.o(.data)
    Control_Switch_Flag                      0x200004cb   Data           1  pub.o(.data)
    nScrNum                                  0x200004cc   Data           2  pub.o(.data)
    Van_Vf0                                  0x200004d0   Data           4  pub.o(.data)
    Online_Flag                              0x200004d4   Data           1  pub.o(.data)
    Online_Old_Flag                          0x200004d5   Data           1  pub.o(.data)
    Standby_Flag                             0x200004d6   Data           1  pub.o(.data)
    Bak_Standby_Flag                         0x200004d7   Data           1  pub.o(.data)
    Standby_Offline_Delay                    0x200004d8   Data           4  pub.o(.data)
    Standby_Online_Delay                     0x200004dc   Data           4  pub.o(.data)
    Start_Ex_Flag                            0x200004e0   Data           1  pub.o(.data)
    Start_Count                              0x200004e1   Data           1  pub.o(.data)
    Stop_Delay_Count                         0x200004e4   Data           4  pub.o(.data)
    Init_Ex_Time_Count                       0x200004e8   Data           4  pub.o(.data)
    Soft_Ref                                 0x200004ec   Data           4  pub.o(.data)
    Stop_Machine_Flag                        0x200004f0   Data           1  pub.o(.data)
    Auto_Ex_Fail_Flag                        0x200004f1   Data           1  pub.o(.data)
    AVR_Flag                                 0x200004f2   Data           1  pub.o(.data)
    FCR_Flag                                 0x200004f3   Data           1  pub.o(.data)
    AER_Flag                                 0x200004f4   Data           1  pub.o(.data)
    Breaker_Flag                             0x200004f5   Data           1  pub.o(.data)
    Breaker_Old_Flag                         0x200004f6   Data           1  pub.o(.data)
    PT_Break_Count                           0x200004f8   Data           4  pub.o(.data)
    PT_Break_Flag                            0x200004fc   Data           1  pub.o(.data)
    PT_Online_Delay                          0x20000500   Data           4  pub.o(.data)
    PT_Switch_Flag                           0x20000504   Data           1  pub.o(.data)
    Load_Limit_Flag                          0x20000505   Data           1  pub.o(.data)
    Over_Ex_Flag                             0x20000506   Data           1  pub.o(.data)
    Over_Ex_Count                            0x20000508   Data           4  pub.o(.data)
    Over_Ex_Ifd_Ref                          0x2000050c   Data           4  pub.o(.data)
    Scr_Exit_Delay                           0x20000510   Data           4  pub.o(.data)
    Ifd_Max_Limit                            0x20000514   Data           4  pub.o(.data)
    Ifd_Sum                                  0x20000518   Data           4  pub.o(.data)
    Ifd_Sum_av                               0x2000051c   Data           4  pub.o(.data)
    Stop_Count                               0x20000520   Data           2  pub.o(.data)
    eIfd_Ref                                 0x20000524   Data           4  pub.o(.data)
    Stop_Command                             0x20000528   Data           1  pub.o(.data)
    Stop_Command_Old                         0x20000529   Data           1  pub.o(.data)
    Pulse_Low_F_Flag                         0x2000052a   Data           1  pub.o(.data)
    Pulse_Low_F_Count                        0x2000052c   Data           4  pub.o(.data)
    Scr_Pulse_Flag                           0x20000530   Data           5  pub.o(.data)
    Asyn_Tx_Flag                             0x20000535   Data           1  pub.o(.data)
    Asyn_Buf_Flag                            0x20000536   Data           1  pub.o(.data)
    Status_Delay                             0x20000538   Data           4  pub.o(.data)
    UI_Ref_Up_Flag                           0x2000053c   Data           1  pub.o(.data)
    UI_Ref_Down_Flag                         0x2000053d   Data           1  pub.o(.data)
    UI_Stop_Flag                             0x2000053e   Data           1  pub.o(.data)
    Net_Monitor_C                            0x20000540   Data           4  pub.o(.data)
    Net_Monitor_Index                        0x20000544   Data           1  pub.o(.data)
    ms_C                                     0x20000548   Data           4  pub.o(.data)
    Monitor_Upload_Flag                      0x2000054c   Data           1  pub.o(.data)
    Upload_Sensor_ID_Flag                    0x2000054d   Data           1  pub.o(.data)
    Sensor_ID_Des_Ch                         0x2000054e   Data           1  pub.o(.data)
    Syn_Switch_Delay                         0x20000550   Data           4  pub.o(.data)
    Sec10Start                               0x20000554   Data           4  pub.o(.data)
    Sec10Elapse                              0x20000558   Data           4  pub.o(.data)
    Flag10Sec                                0x2000055c   Data           1  pub.o(.data)
    IfdSumAvag                               0x20000560   Data           4  pub.o(.data)
    AlphaAddValue                            0x20000564   Data           4  pub.o(.data)
    IfdDifference                            0x20000568   Data           4  pub.o(.data)
    Syn_A_Old_t                              0x2000056c   Data           4  tim.o(.data)
    Syn_B_Old_t                              0x20000570   Data           4  tim.o(.data)
    Syn_C_Old_t                              0x20000574   Data           4  tim.o(.data)
    Low_Level_Period                         0x20000578   Data           4  tim.o(.data)
    Syn_BA_Time                              0x2000057c   Data           4  tim.o(.data)
    Syn_CB_Time                              0x20000580   Data           4  tim.o(.data)
    Syn_AC_Time                              0x20000584   Data           4  tim.o(.data)
    Pulse_Data                               0x20000588   Data          12  tim.o(.data)
    Net_Rx_Buf                               0x20000594   Data        8192  internet.o(.bss)
    SPI_Tx_Buf                               0x20002594   Data        8200  internet.o(.bss)
    SPI_Rx_Buf                               0x2000459c   Data        8200  internet.o(.bss)
    Net_Port                                 0x200065a4   Data          32  internet.o(.bss)
    Net1_Data                                0x200065c4   Data          60  internet.o(.bss)
    Net2_Data                                0x20006600   Data          60  internet.o(.bss)
    Net1_Sock_Reg                            0x2000663c   Data         480  internet.o(.bss)
    Net2_Sock_Reg                            0x2000681c   Data         480  internet.o(.bss)
    Net_IF0                                  0x200069fc   Data         340  internet.o(.bss)
    Net_IF1                                  0x20006b50   Data         384  internet.o(.bss)
    Period_Source                            0x20006cd0   Data          20  pub.o(.bss)
    chSCR                                    0x20006ce4   Data          76  pub.o(.bss)
    chEx                                     0x20006d30   Data         152  pub.o(.bss)
    Iarm                                     0x20006dc8   Data          24  pub.o(.bss)
    PID_T                                    0x20006de0   Data          16  pub.o(.bss)
    AI_Offset                                0x20006df0   Data          32  pub.o(.bss)
    eIfd_Count                               0x20006e10   Data          12  pub.o(.bss)
    Scr_Pulse_Count                          0x20006e1c   Data          20  pub.o(.bss)
    AsynTxData                               0x20006e30   Data         148  pub.o(.bss)
    AsynTxBuf                                0x20006ec4   Data         148  pub.o(.bss)
    AsynRxData                               0x20006f58   Data          20  pub.o(.bss)
    EE_Data                                  0x20006f6c   Data         256  pub.o(.bss)
    ucMornitor                               0x2000706c   Data       16384  pub.o(.bss)
    ucQTx                                    0x2000b06c   Data         128  pub.o(.bss)
    Sensor_ID                                0x2000b0ec   Data          60  pub.o(.bss)
    Tarm                                     0x2000b128   Data          60  pub.o(.bss)
    T_c                                      0x2000b164   Data          60  pub.o(.bss)
    T_Flag                                   0x2000b1a0   Data          15  pub.o(.bss)
    t_Op                                     0x2000b1af   Data           9  pub.o(.bss)
    Rising_Edge_Time                         0x2000b1b8   Data          28  tim.o(.bss)
    __libspace_start                         0x2000b1d4   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x2000b234   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000042d4, Max: 0x00200000, ABSOLUTE, COMPRESSED[0x00003e80])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00003d40, Max: 0x00200000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO         5656    RESET               startup_gd32f450.o
    0x080001ac   0x080001ac   0x00000008   Code   RO         7626  * !!!main             c_w.l(__main.o)
    0x080001b4   0x080001b4   0x00000034   Code   RO         7936    !!!scatter          c_w.l(__scatter.o)
    0x080001e8   0x080001e8   0x0000005a   Code   RO         7934    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x08000242   0x08000242   0x00000002   PAD
    0x08000244   0x08000244   0x0000001c   Code   RO         7938    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000260   0x08000260   0x00000002   Code   RO         7803    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000262   0x08000262   0x00000004   Code   RO         7808    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7811    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7814    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7816    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7818    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7821    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7823    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7825    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7827    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7829    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7831    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7833    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7835    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7837    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7839    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7841    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7845    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7847    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7849    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000000   Code   RO         7851    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000266   0x08000266   0x00000002   Code   RO         7852    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000268   0x08000268   0x00000002   Code   RO         7874    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x0800026a   0x0800026a   0x00000000   Code   RO         7887    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x0800026a   0x0800026a   0x00000000   Code   RO         7889    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x0800026a   0x0800026a   0x00000000   Code   RO         7892    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x0800026a   0x0800026a   0x00000000   Code   RO         7895    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x0800026a   0x0800026a   0x00000000   Code   RO         7897    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x0800026a   0x0800026a   0x00000000   Code   RO         7900    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x0800026a   0x0800026a   0x00000002   Code   RO         7901    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x0800026c   0x0800026c   0x00000000   Code   RO         7706    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x0800026c   0x0800026c   0x00000000   Code   RO         7766    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x0800026c   0x0800026c   0x00000006   Code   RO         7778    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000272   0x08000272   0x00000000   Code   RO         7768    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000272   0x08000272   0x00000004   Code   RO         7769    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000276   0x08000276   0x00000000   Code   RO         7771    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000276   0x08000276   0x00000008   Code   RO         7772    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800027e   0x0800027e   0x00000002   Code   RO         7806    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000280   0x08000280   0x00000000   Code   RO         7856    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000280   0x08000280   0x00000004   Code   RO         7857    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000284   0x08000284   0x00000006   Code   RO         7858    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x0800028a   0x0800028a   0x00000002   PAD
    0x0800028c   0x0800028c   0x00000040   Code   RO         5657    .text               startup_gd32f450.o
    0x080002cc   0x080002cc   0x00000006   Code   RO         7624    .text               c_w.l(heapauxi.o)
    0x080002d2   0x080002d2   0x00000002   PAD
    0x080002d4   0x080002d4   0x00000008   Code   RO         7791    .text               c_w.l(libspace.o)
    0x080002dc   0x080002dc   0x0000004a   Code   RO         7794    .text               c_w.l(sys_stackheap_outer.o)
    0x08000326   0x08000326   0x00000012   Code   RO         7796    .text               c_w.l(exit.o)
    0x08000338   0x08000338   0x0000000c   Code   RO         7866    .text               c_w.l(sys_exit.o)
    0x08000344   0x08000344   0x00000002   Code   RO         7877    .text               c_w.l(use_no_semi.o)
    0x08000346   0x08000346   0x00000000   Code   RO         7879    .text               c_w.l(indicate_semi.o)
    0x08000346   0x08000346   0x0000000a   Code   RO         6586    i.AD                ai.o
    0x08000350   0x08000350   0x00000044   Code   RO         6137    i.AD7606_Read_Reg   cl1606_spi.o
    0x08000394   0x08000394   0x00000028   Code   RO         6139    i.AD7606_Start_Convst  cl1606_spi.o
    0x080003bc   0x080003bc   0x0000002c   Code   RO         6923    i.Boot_Delay        main.o
    0x080003e8   0x080003e8   0x00000026   Code   RO         7010    i.Byte_To_Float     optical.o
    0x0800040e   0x0800040e   0x00000002   PAD
    0x08000410   0x08000410   0x000000dc   Code   RO         7340    i.Cal_Period        syn.o
    0x080004ec   0x080004ec   0x00000050   Code   RO         7341    i.Check_Syn_Error   syn.o
    0x0800053c   0x0800053c   0x0000006c   Code   RO         7342    i.Clear_Syn_Error   syn.o
    0x080005a8   0x080005a8   0x00000082   Code   RO         6243    i.Config_W5500      internet.o
    0x0800062a   0x0800062a   0x0000000c   Code   RO         6144    i.Delay_Func        cl1606_spi.o
    0x08000636   0x08000636   0x0000000c   Code   RO         6244    i.Delay_W5100       internet.o
    0x08000642   0x08000642   0x00000002   PAD
    0x08000644   0x08000644   0x00000014   Code   RO         7015    i.Dyn_Comp_Timer_Init  optical.o
    0x08000658   0x08000658   0x000001b4   Code   RO         7486    i.First_Rising_Edge  tim.o
    0x0800080c   0x0800080c   0x000000e4   Code   RO         7487    i.Generate_Pulse    tim.o
    0x080008f0   0x080008f0   0x00000058   Code   RO         7488    i.Generate_Tx_Flag  tim.o
    0x08000948   0x08000948   0x00000002   Code   RO         7197    i.Init_Data         para.o
    0x0800094a   0x0800094a   0x00000008   Code   RO         7198    i.Init_Hardware     para.o
    0x08000952   0x08000952   0x00000002   PAD
    0x08000954   0x08000954   0x00000158   Code   RO         6975    i.Init_Net          net.o
    0x08000aac   0x08000aac   0x00000144   Code   RO         6976    i.Net_Check         net.o
    0x08000bf0   0x08000bf0   0x00000064   Code   RO         6245    i.Net_Comm          internet.o
    0x08000c54   0x08000c54   0x000000a8   Code   RO         6246    i.Net_Cycle         internet.o
    0x08000cfc   0x08000cfc   0x000000d8   Code   RO         6247    i.Net_DMA_Rx        internet.o
    0x08000dd4   0x08000dd4   0x000000e8   Code   RO         6248    i.Net_DMA_Tx        internet.o
    0x08000ebc   0x08000ebc   0x0000024c   Code   RO         6249    i.Net_DMA_rx_process  internet.o
    0x08001108   0x08001108   0x00000214   Code   RO         6250    i.Net_DMA_tx_process  internet.o
    0x0800131c   0x0800131c   0x000001d8   Code   RO         6251    i.Net_Interrupt_Process  internet.o
    0x080014f4   0x080014f4   0x000000f4   Code   RO         6252    i.Net_Mode_Select   internet.o
    0x080015e8   0x080015e8   0x0000006c   Code   RO         6977    i.Net_Order         net.o
    0x08001654   0x08001654   0x000000cc   Code   RO         6253    i.Net_Para_Init     internet.o
    0x08001720   0x08001720   0x000002ac   Code   RO         6254    i.Net_Rx            internet.o
    0x080019cc   0x080019cc   0x00000054   Code   RO         6255    i.Net_State_Check   internet.o
    0x08001a20   0x08001a20   0x00000270   Code   RO         6256    i.Net_Tx            internet.o
    0x08001c90   0x08001c90   0x00000230   Code   RO         6271    i.Network_Init      internet.o
    0x08001ec0   0x08001ec0   0x00000028   Code   RO         7344    i.Phase_Difference  syn.o
    0x08001ee8   0x08001ee8   0x00000150   Code   RO         7345    i.Primary_Syn_Check  syn.o
    0x08002038   0x08002038   0x00000040   Code   RO         7346    i.Primary_Syn_Switch_Check  syn.o
    0x08002078   0x08002078   0x000000e8   Code   RO         7347    i.Pulse_Syn_Error_Check  syn.o
    0x08002160   0x08002160   0x0000004c   Code   RO         6145    i.Read_AD_Sample    cl1606_spi.o
    0x080021ac   0x080021ac   0x000000b0   Code   RO         6272    i.Read_Byte_W5500   internet.o
    0x0800225c   0x0800225c   0x000000bc   Code   RO         6273    i.Read_SOCK_Byte1_W5500  internet.o
    0x08002318   0x08002318   0x000000f8   Code   RO         6274    i.Read_SOCK_Byte2_W5500  internet.o
    0x08002410   0x08002410   0x0000004e   Code   RO         6148    i.SCT_AD_R_Data     cl1606_spi.o
    0x0800245e   0x0800245e   0x00000002   PAD
    0x08002460   0x08002460   0x000000cc   Code   RO         6277    i.SPI1_DMA_Config   internet.o
    0x0800252c   0x0800252c   0x000000cc   Code   RO         6278    i.SPI2_DMA_Config   internet.o
    0x080025f8   0x080025f8   0x000000f0   Code   RO         6279    i.SPI4_For_W5200_Init  internet.o
    0x080026e8   0x080026e8   0x00000100   Code   RO         6280    i.SPI5_For_W5200_Init  internet.o
    0x080027e8   0x080027e8   0x00000044   Code   RO         6150    i.SPIx_RW_One_Byte  cl1606_spi.o
    0x0800282c   0x0800282c   0x0000007c   Code   RO         7349    i.Sec_Syn_Check     syn.o
    0x080028a8   0x080028a8   0x00000058   Code   RO         7350    i.SelectSyn         syn.o
    0x08002900   0x08002900   0x00000168   Code   RO         7033    i.SelectSynSource   optical.o
    0x08002a68   0x08002a68   0x00000068   Code   RO         7490    i.Set_Alpha_Delay   tim.o
    0x08002ad0   0x08002ad0   0x00000120   Code   RO         6285    i.Socket_Correct_Addr  internet.o
    0x08002bf0   0x08002bf0   0x00000082   Code   RO         6286    i.Socket_Mode       internet.o
    0x08002c72   0x08002c72   0x0000000c   Code   RO         7202    i.Soft_Delay        para.o
    0x08002c7e   0x08002c7e   0x00000002   PAD
    0x08002c80   0x08002c80   0x00000028   Code   RO         7351    i.Syn_Check         syn.o
    0x08002ca8   0x08002ca8   0x000000b8   Code   RO         5615    i.SystemInit        system_gd32f4xx.o
    0x08002d60   0x08002d60   0x000000d0   Code   RO         7494    i.TIM3_Update       tim.o
    0x08002e30   0x08002e30   0x00000080   Code   RO         7495    i.TIM4_CC1          tim.o
    0x08002eb0   0x08002eb0   0x00000080   Code   RO         7496    i.TIM4_CC2          tim.o
    0x08002f30   0x08002f30   0x00000080   Code   RO         7497    i.TIM4_CC4          tim.o
    0x08002fb0   0x08002fb0   0x00000074   Code   RO         7500    i.TIMER0_BRK_TIMER8_IRQHandler  tim.o
    0x08003024   0x08003024   0x00000012   Code   RO         7501    i.TIMER1_IRQHandler  tim.o
    0x08003036   0x08003036   0x00000002   PAD
    0x08003038   0x08003038   0x00000014   Code   RO         7502    i.TIMER2_IRQHandler  tim.o
    0x0800304c   0x0800304c   0x00000018   Code   RO         7503    i.TIMER3_IRQHandler  tim.o
    0x08003064   0x08003064   0x00000034   Code   RO         7504    i.TIMER4_IRQHandler  tim.o
    0x08003098   0x08003098   0x0000002c   Code   RO         6287    i.W5200U1_RST_Init  internet.o
    0x080030c4   0x080030c4   0x00000010   Code   RO         6288    i.W5200U1_RST_Off   internet.o
    0x080030d4   0x080030d4   0x0000002c   Code   RO         6289    i.W5200U2_RST_Init  internet.o
    0x08003100   0x08003100   0x00000010   Code   RO         6290    i.W5200U2_RST_Off   internet.o
    0x08003110   0x08003110   0x000000a0   Code   RO         6293    i.Write_Byte1_W5500  internet.o
    0x080031b0   0x080031b0   0x000000c0   Code   RO         6294    i.Write_Byte2_W5500  internet.o
    0x08003270   0x08003270   0x000000ac   Code   RO         6295    i.Write_SOCK_Byte1_W5500  internet.o
    0x0800331c   0x0800331c   0x000000cc   Code   RO         6296    i.Write_SOCK_Byte2_W5500  internet.o
    0x080033e8   0x080033e8   0x00000112   Code   RO         6297    i.Write_SOCK_Byte4_W5500  internet.o
    0x080034fa   0x080034fa   0x000000c0   Code   RO         6298    i.Write_W5500       internet.o
    0x080035ba   0x080035ba   0x00000020   Code   RO         1192    i.dma_channel_disable  gd32f4xx_dma.o
    0x080035da   0x080035da   0x00000020   Code   RO         1193    i.dma_channel_enable  gd32f4xx_dma.o
    0x080035fa   0x080035fa   0x00000026   Code   RO         1194    i.dma_channel_subperipheral_select  gd32f4xx_dma.o
    0x08003620   0x08003620   0x000000a4   Code   RO         1197    i.dma_deinit        gd32f4xx_dma.o
    0x080036c4   0x080036c4   0x0000003e   Code   RO         1199    i.dma_flag_clear    gd32f4xx_dma.o
    0x08003702   0x08003702   0x0000004c   Code   RO         1200    i.dma_flag_get      gd32f4xx_dma.o
    0x0800374e   0x0800374e   0x00000002   PAD
    0x08003750   0x08003750   0x00000158   Code   RO         1217    i.dma_single_data_mode_init  gd32f4xx_dma.o
    0x080038a8   0x080038a8   0x00000022   Code   RO         1218    i.dma_single_data_para_struct_init  gd32f4xx_dma.o
    0x080038ca   0x080038ca   0x0000005e   Code   RO         2599    i.gpio_af_set       gd32f4xx_gpio.o
    0x08003928   0x08003928   0x00000004   Code   RO         2600    i.gpio_bit_reset    gd32f4xx_gpio.o
    0x0800392c   0x0800392c   0x00000004   Code   RO         2601    i.gpio_bit_set      gd32f4xx_gpio.o
    0x08003930   0x08003930   0x00000010   Code   RO         2605    i.gpio_input_bit_get  gd32f4xx_gpio.o
    0x08003940   0x08003940   0x0000004e   Code   RO         2607    i.gpio_mode_set     gd32f4xx_gpio.o
    0x0800398e   0x0800398e   0x00000042   Code   RO         2609    i.gpio_output_options_set  gd32f4xx_gpio.o
    0x080039d0   0x080039d0   0x00000030   Code   RO         6924    i.main              main.o
    0x08003a00   0x08003a00   0x00000014   Code   RO         3173    i.nvic_priority_group_set  gd32f4xx_misc.o
    0x08003a14   0x08003a14   0x00000024   Code   RO         3396    i.rcu_periph_clock_enable  gd32f4xx_rcu.o
    0x08003a38   0x08003a38   0x00000024   Code   RO         3399    i.rcu_periph_reset_disable  gd32f4xx_rcu.o
    0x08003a5c   0x08003a5c   0x00000024   Code   RO         3400    i.rcu_periph_reset_enable  gd32f4xx_rcu.o
    0x08003a80   0x08003a80   0x00000004   Code   RO         4232    i.spi_crc_polynomial_set  gd32f4xx_spi.o
    0x08003a84   0x08003a84   0x0000000a   Code   RO         4233    i.spi_disable       gd32f4xx_spi.o
    0x08003a8e   0x08003a8e   0x00000016   Code   RO         4234    i.spi_dma_disable   gd32f4xx_spi.o
    0x08003aa4   0x08003aa4   0x00000016   Code   RO         4235    i.spi_dma_enable    gd32f4xx_spi.o
    0x08003aba   0x08003aba   0x0000000a   Code   RO         4236    i.spi_enable        gd32f4xx_spi.o
    0x08003ac4   0x08003ac4   0x00000008   Code   RO         4238    i.spi_i2s_data_receive  gd32f4xx_spi.o
    0x08003acc   0x08003acc   0x00000004   Code   RO         4239    i.spi_i2s_data_transmit  gd32f4xx_spi.o
    0x08003ad0   0x08003ad0   0x000000ac   Code   RO         4240    i.spi_i2s_deinit    gd32f4xx_spi.o
    0x08003b7c   0x08003b7c   0x00000010   Code   RO         4241    i.spi_i2s_flag_get  gd32f4xx_spi.o
    0x08003b8c   0x08003b8c   0x00000032   Code   RO         4245    i.spi_init          gd32f4xx_spi.o
    0x08003bbe   0x08003bbe   0x0000000a   Code   RO         4249    i.spi_nss_output_enable  gd32f4xx_spi.o
    0x08003bc8   0x08003bc8   0x000000fc   Code   RO         5616    i.system_clock_200m_25m_hxtal  system_gd32f4xx.o
    0x08003cc4   0x08003cc4   0x00000008   Code   RO         5617    i.system_clock_config  system_gd32f4xx.o
    0x08003ccc   0x08003ccc   0x0000000a   Code   RO         4563    i.timer_disable     gd32f4xx_timer.o
    0x08003cd6   0x08003cd6   0x0000000a   Code   RO         4567    i.timer_enable      gd32f4xx_timer.o
    0x08003ce0   0x08003ce0   0x00000006   Code   RO         4574    i.timer_flag_clear  gd32f4xx_timer.o
    0x08003ce6   0x08003ce6   0x00000008   Code   RO         4583    i.timer_interrupt_disable  gd32f4xx_timer.o
    0x08003cee   0x08003cee   0x00000008   Code   RO         4584    i.timer_interrupt_enable  gd32f4xx_timer.o
    0x08003cf6   0x08003cf6   0x00000006   Code   RO         4585    i.timer_interrupt_flag_clear  gd32f4xx_timer.o
    0x08003cfc   0x08003cfc   0x00000018   Code   RO         4586    i.timer_interrupt_flag_get  gd32f4xx_timer.o
    0x08003d14   0x08003d14   0x0000000a   Code   RO         7864    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08003d1e   0x08003d1e   0x00000002   PAD
    0x08003d20   0x08003d20   0x00000020   Data   RO         7932    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08003d40, Size: 0x0000ba38, Max: 0x00070000, ABSOLUTE, COMPRESSED[0x00000140])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000070   Data   RW         6301    .data               internet.o
    0x20000070   COMPRESSED   0x00000004   Data   RW         6978    .data               net.o
    0x20000074   COMPRESSED   0x00000264   Data   RW         7038    .data               optical.o
    0x200002d8   COMPRESSED   0x00000294   Data   RW         7289    .data               pub.o
    0x2000056c   COMPRESSED   0x00000028   Data   RW         7507    .data               tim.o
    0x20000594        -       0x0000673c   Zero   RW         6299    .bss                internet.o
    0x20006cd0        -       0x000044e8   Zero   RW         7288    .bss                pub.o
    0x2000b1b8        -       0x0000001c   Zero   RW         7506    .bss                tim.o
    0x2000b1d4        -       0x00000060   Zero   RW         7792    .bss                c_w.l(libspace.o)
    0x2000b234   COMPRESSED   0x00000004   PAD
    0x2000b238        -       0x00000400   Zero   RW         5655    HEAP                startup_gd32f450.o
    0x2000b638        -       0x00000400   Zero   RW         5654    STACK               startup_gd32f450.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        10          0          0          0          0        513   ai.o
       342         24          0          0          0       4776   cl1606_spi.o
         0          0          0          0          0      48432   gd32f4xx_adc.o
       782          4          0          0          0       5626   gd32f4xx_dma.o
       262          0          0          0          0       4121   gd32f4xx_gpio.o
        20         10          0          0          0        554   gd32f4xx_misc.o
       108         12          0          0          0       1655   gd32f4xx_rcu.o
       328         10          0          0          0       6913   gd32f4xx_spi.o
        72          0          0          0          0       4133   gd32f4xx_timer.o
      8098        518          0        112      26428      36977   internet.o
        92         10          0          0          0       1027   main.o
       776        122          0          4          0       2382   net.o
       418         28          0        612          0       4872   optical.o
        22          0          0          0          0       1461   para.o
         0          0          0        660      17640       9581   pub.o
        64         26        428          0       2048        904   startup_gd32f450.o
      1332        280          0          0          0       6671   syn.o
       444         28          0          0          0       2369   system_gd32f4xx.o
      1678        336          0         40         28       8019   tim.o

    ----------------------------------------------------------------------
     14862       <USER>        <GROUP>       1428      46144     150986   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        14          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         6          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        10          0          0          0          0        116   fpinit.o

    ----------------------------------------------------------------------
       358         <USER>          <GROUP>          0        100        700   Library Totals
         8          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       340         16          0          0         96        584   c_w.l
        10          0          0          0          0        116   fz_wm.l

    ----------------------------------------------------------------------
       358         <USER>          <GROUP>          0        100        700   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     15220       1424        460       1428      46244     140730   Grand Totals
     15220       1424        460        320      46244     140730   ELF Image Totals (compressed)
     15220       1424        460        320          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                15680 (  15.31kB)
    Total RW  Size (RW Data + ZI Data)             47672 (  46.55kB)
    Total ROM Size (Code + RO Data + RW Data)      16000 (  15.63kB)

==============================================================================

